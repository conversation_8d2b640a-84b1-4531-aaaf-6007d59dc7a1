# AlgoFactory - Project Structure

## Directory Structure

```
algofactory/
├── docs/                           # Documentation
│   ├── README.md                   # Main documentation index
│   ├── 01_project_overview.md      # Project vision and business model
│   ├── 02_technical_architecture.md # System architecture and design
│   ├── 03_instance_management.md   # Instance lifecycle management
│   ├── 04_user_dashboard.md        # Frontend dashboard design
│   ├── 05_api_gateway.md          # API gateway implementation
│   ├── 06_deployment_guide.md     # Production deployment guide
│   └── 07_development_roadmap.md  # Development timeline and milestones
├── src/                           # Source code
│   ├── gateway/                   # API Gateway service
│   │   ├── main.py               # FastAPI application
│   │   ├── middleware/           # Custom middleware
│   │   ├── routers/              # API route handlers
│   │   ├── services/             # Business logic services
│   │   └── models/               # Data models
│   ├── frontend/                 # React frontend application
│   │   ├── src/
│   │   │   ├── components/       # React components
│   │   │   ├── hooks/            # Custom React hooks
│   │   │   ├── services/         # API service calls
│   │   │   └── styles/           # CSS/SCSS styles
│   │   ├── public/               # Static assets
│   │   └── package.json          # Node.js dependencies
│   ├── workers/                  # Background task workers
│   │   ├── celery.py            # Celery configuration
│   │   ├── instance_manager.py   # Instance management tasks
│   │   └── monitoring.py         # Health monitoring tasks
│   └── database/                 # Database related code
│       ├── models.py             # SQLAlchemy models
│       ├── migrations/           # Alembic migrations
│       └── seeds.py              # Database seed data
├── infrastructure/               # Infrastructure as Code
│   ├── terraform/               # Terraform configurations
│   │   ├── main.tf              # Main infrastructure
│   │   ├── variables.tf         # Variable definitions
│   │   └── outputs.tf           # Output values
│   ├── docker/                  # Docker configurations
│   │   ├── Dockerfile.gateway   # Gateway service container
│   │   ├── Dockerfile.frontend  # Frontend container
│   │   └── docker-compose.yml   # Development environment
│   └── scripts/                 # Deployment and utility scripts
│       ├── deploy.sh            # Deployment script
│       ├── backup.sh            # Backup script
│       └── setup.sh             # Environment setup
├── tests/                       # Test suites
│   ├── unit/                    # Unit tests
│   ├── integration/             # Integration tests
│   └── e2e/                     # End-to-end tests
├── monitoring/                  # Monitoring configurations
│   ├── prometheus.yml           # Prometheus configuration
│   ├── grafana/                 # Grafana dashboards
│   └── alerts/                  # Alert rules
├── .env.example                 # Environment variables template
├── requirements.txt             # Python dependencies
├── docker-compose.yml           # Development environment
├── docker-compose.prod.yml      # Production environment
└── README.md                    # Project README
```

## Key Components

### 1. API Gateway (`src/gateway/`)
The central service that handles all client requests and routes them to appropriate OpenAlgo instances.

**Key Files:**
- `main.py` - FastAPI application entry point
- `middleware/auth.py` - Authentication middleware
- `middleware/rate_limit.py` - Rate limiting middleware
- `services/proxy.py` - Request routing service
- `services/instance.py` - Instance management service

### 2. Frontend (`src/frontend/`)
React-based web application providing the user interface.

**Key Components:**
- `components/Dashboard/` - Main dashboard components
- `components/Instance/` - Instance management components
- `hooks/useAuth.js` - Authentication hook
- `hooks/useInstances.js` - Instance management hook
- `services/api.js` - API client service

### 3. Workers (`src/workers/`)
Background task processors for instance management and monitoring.

**Key Workers:**
- `instance_manager.py` - Instance lifecycle management
- `monitoring.py` - Health monitoring and metrics collection
- `billing.py` - Billing and usage tracking

### 4. Infrastructure (`infrastructure/`)
Infrastructure as Code and deployment configurations.

**Components:**
- `terraform/` - AWS infrastructure definitions
- `docker/` - Container configurations
- `scripts/` - Automation scripts

## Development Workflow

### 1. Local Development Setup

```bash
# Clone repository
git clone https://github.com/yourusername/algofactory.git
cd algofactory

# Setup Python environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt

# Setup Node.js environment
cd src/frontend
npm install
cd ../..

# Setup environment variables
cp .env.example .env
# Edit .env with your configuration

# Start development services
docker-compose up -d postgres redis

# Run database migrations
alembic upgrade head

# Start development servers
# Terminal 1: Backend
uvicorn src.gateway.main:app --reload --port 8000

# Terminal 2: Frontend
cd src/frontend && npm start

# Terminal 3: Workers
celery -A src.workers.celery worker --loglevel=info
```

### 2. Testing

```bash
# Run unit tests
pytest tests/unit/

# Run integration tests
pytest tests/integration/

# Run end-to-end tests
pytest tests/e2e/

# Run with coverage
pytest --cov=src tests/
```

### 3. Code Quality

```bash
# Python code formatting
black src/
isort src/

# Python linting
flake8 src/

# Frontend linting
cd src/frontend
npm run lint
npm run format
```

## Configuration Management

### Environment Variables

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/algofactory
REDIS_URL=redis://localhost:6379/0

# JWT Configuration
JWT_SECRET_KEY=your-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# AWS Configuration
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_DEFAULT_REGION=us-east-1

# Application Settings
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ORIGINS=http://localhost:3000
```

### Configuration Files

- `.env` - Environment-specific variables
- `src/gateway/config.py` - Application configuration
- `src/frontend/.env` - Frontend environment variables
- `docker-compose.yml` - Development environment
- `docker-compose.prod.yml` - Production environment

## Deployment Strategy

### 1. Development Environment
- Local development with Docker Compose
- Hot reloading for both frontend and backend
- Local PostgreSQL and Redis instances

### 2. Staging Environment
- AWS-based infrastructure
- Automated deployment from develop branch
- Production-like configuration for testing

### 3. Production Environment
- Multi-AZ AWS deployment
- Blue-green deployment strategy
- Automated backups and monitoring

## Monitoring and Observability

### Metrics Collection
- Application metrics via Prometheus
- Business metrics via custom collectors
- Infrastructure metrics via CloudWatch

### Logging
- Structured logging with JSON format
- Centralized logging with ELK stack
- Log aggregation from all services

### Alerting
- Critical system alerts
- Business metric alerts
- Performance degradation alerts

## Security Considerations

### Data Protection
- Encryption at rest and in transit
- Secure credential storage
- Regular security audits

### Access Control
- Role-based access control (RBAC)
- API key management
- Multi-factor authentication

### Compliance
- Data privacy compliance
- Audit logging
- Security monitoring

This project structure provides a solid foundation for building a scalable, maintainable, and secure multi-tenant trading platform.
