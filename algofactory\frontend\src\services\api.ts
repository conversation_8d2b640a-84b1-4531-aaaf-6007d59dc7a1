import axios, { AxiosInstance, AxiosResponse } from 'axios';

// API configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      // Try to refresh token
      const refreshToken = localStorage.getItem('refreshToken');
      if (refreshToken) {
        try {
          const response = await api.post('/auth/refresh', {
            refresh_token: refreshToken,
          });
          
          const { access_token, refresh_token } = response.data;
          localStorage.setItem('token', access_token);
          localStorage.setItem('refreshToken', refresh_token);
          
          // Retry original request
          originalRequest.headers.Authorization = `Bearer ${access_token}`;
          return api(originalRequest);
        } catch (refreshError) {
          // Refresh failed, redirect to login
          localStorage.removeItem('token');
          localStorage.removeItem('refreshToken');
          window.location.href = '/login';
        }
      } else {
        // No refresh token, redirect to login
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// API service functions
export const authAPI = {
  register: (userData: {
    email: string;
    password: string;
    first_name?: string;
    last_name?: string;
  }) => api.post('/auth/register', userData),

  login: (credentials: { username: string; password: string }) =>
    api.post('/auth/login', credentials, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      transformRequest: [(data) => {
        const params = new URLSearchParams();
        params.append('username', data.username);
        params.append('password', data.password);
        return params;
      }],
    }),

  logout: () => api.post('/auth/logout'),

  refreshToken: (refreshToken: string) =>
    api.post('/auth/refresh', { refresh_token: refreshToken }),
};

export const userAPI = {
  getProfile: () => api.get('/users/profile'),
  
  updateProfile: (userData: {
    first_name?: string;
    last_name?: string;
    email?: string;
  }) => api.put('/users/profile', userData),

  getSubscription: () => api.get('/users/subscription'),
};

export const instancesAPI = {
  getInstances: () => api.get('/instances'),
  
  createInstance: (instanceData: {
    name: string;
    broker: string;
  }) => api.post('/instances', instanceData),

  getInstance: (instanceId: string) => api.get(`/instances/${instanceId}`),
  
  updateInstance: (instanceId: string, instanceData: {
    name?: string;
    broker?: string;
  }) => api.put(`/instances/${instanceId}`, instanceData),

  deleteInstance: (instanceId: string) => api.delete(`/instances/${instanceId}`),
  
  startInstance: (instanceId: string) => api.post(`/instances/${instanceId}/start`),
  
  stopInstance: (instanceId: string) => api.post(`/instances/${instanceId}/stop`),
};

export default api;
