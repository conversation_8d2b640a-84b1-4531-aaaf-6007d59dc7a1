"""
Trading Instance schemas for request/response validation
"""

from typing import Optional
from datetime import datetime
from pydantic import BaseModel, validator
import uuid


class TradingInstanceBase(BaseModel):
    """Base trading instance schema"""
    name: str
    broker: str
    
    @validator('name')
    def validate_name(cls, v):
        if len(v) < 3:
            raise ValueError('Instance name must be at least 3 characters long')
        return v


class TradingInstanceCreate(TradingInstanceBase):
    """Schema for trading instance creation"""
    pass


class TradingInstanceUpdate(BaseModel):
    """Schema for trading instance updates"""
    name: Optional[str] = None
    broker: Optional[str] = None


class TradingInstanceInDB(TradingInstanceBase):
    """Schema for trading instance in database"""
    id: uuid.UUID
    user_id: uuid.UUID
    status: str
    lightsail_instance_id: Optional[str] = None
    static_ip: Optional[str] = None
    internal_url: Optional[str] = None
    openalgo_api_key: Optional[str] = None
    created_at: datetime
    last_active: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class TradingInstance(TradingInstanceInDB):
    """Public trading instance schema (response)"""
    pass


class BrokerCredentialBase(BaseModel):
    """Base broker credential schema"""
    broker: str


class BrokerCredentialCreate(BrokerCredentialBase):
    """Schema for broker credential creation"""
    credentials: dict  # Will be encrypted before storage


class BrokerCredential(BrokerCredentialBase):
    """Public broker credential schema (response)"""
    id: uuid.UUID
    instance_id: uuid.UUID
    created_at: datetime
    
    class Config:
        from_attributes = True
