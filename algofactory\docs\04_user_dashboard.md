# AlgoFactory - User Dashboard Design

## Dashboard Overview

The AlgoFactory dashboard provides users with a comprehensive view of their trading instances, real-time performance metrics, and management capabilities. The interface is designed to be intuitive while providing powerful functionality for managing multiple trading instances.

## Dashboard Layout

### Main Navigation
```
┌─────────────────────────────────────────────────────────────────┐
│ AlgoFactory Logo    [Dashboard] [Instances] [Analytics] [Profile]│
│                                                    [Notifications]│
└─────────────────────────────────────────────────────────────────┘
```

### Dashboard Sections

#### 1. Overview Cards
```
┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│ Total P&L   │ │ Active      │ │ Total       │ │ Monthly     │
│ +$2,450.50  │ │ Instances   │ │ Trades      │ │ Cost        │
│ ↗ +12.5%    │ │     3       │ │    156      │ │   $87       │
└─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘
```

#### 2. Instance Management Grid
```
┌─────────────────────────────────────────────────────────────────┐
│ My Trading Instances                           [+ Create New]    │
├─────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ Instance: Zerodha-Main        Status: ● Active             │ │
│ │ Broker: Zerodha              IP: ************             │ │
│ │ P&L: +$1,250.30 (↗ +8.5%)   Trades: 45                   │ │
│ │ [View] [Manage] [Stop] [Analytics]                        │ │
│ └─────────────────────────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ Instance: Angel-Scalping     Status: ● Active             │ │
│ │ Broker: Angel One            IP: ************             │ │
│ │ P&L: +$890.20 (↗ +15.2%)    Trades: 78                   │ │
│ │ [View] [Manage] [Stop] [Analytics]                        │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## React Component Structure

### Main Dashboard Component

```typescript
// src/components/Dashboard/Dashboard.tsx
import React, { useState, useEffect } from 'react';
import { Grid, Card, CardContent, Typography, Button } from '@mui/material';
import { useAuth } from '../hooks/useAuth';
import { useInstances } from '../hooks/useInstances';
import OverviewCards from './OverviewCards';
import InstanceGrid from './InstanceGrid';
import CreateInstanceModal from './CreateInstanceModal';

interface DashboardProps {}

const Dashboard: React.FC<DashboardProps> = () => {
  const { user } = useAuth();
  const { instances, loading, refreshInstances } = useInstances();
  const [createModalOpen, setCreateModalOpen] = useState(false);

  useEffect(() => {
    refreshInstances();
  }, []);

  return (
    <div className="dashboard-container">
      <div className="dashboard-header">
        <Typography variant="h4" component="h1">
          Welcome back, {user?.firstName}
        </Typography>
        <Button
          variant="contained"
          color="primary"
          onClick={() => setCreateModalOpen(true)}
        >
          Create New Instance
        </Button>
      </div>

      <OverviewCards instances={instances} />
      
      <InstanceGrid 
        instances={instances} 
        loading={loading}
        onRefresh={refreshInstances}
      />

      <CreateInstanceModal
        open={createModalOpen}
        onClose={() => setCreateModalOpen(false)}
        onSuccess={refreshInstances}
      />
    </div>
  );
};

export default Dashboard;
```

### Overview Cards Component

```typescript
// src/components/Dashboard/OverviewCards.tsx
import React from 'react';
import { Grid, Card, CardContent, Typography, Box } from '@mui/material';
import { TrendingUp, TrendingDown } from '@mui/icons-material';

interface OverviewCardsProps {
  instances: Instance[];
}

const OverviewCards: React.FC<OverviewCardsProps> = ({ instances }) => {
  const totalPnL = instances.reduce((sum, instance) => sum + instance.pnl, 0);
  const activeInstances = instances.filter(i => i.status === 'active').length;
  const totalTrades = instances.reduce((sum, instance) => sum + instance.totalTrades, 0);
  const monthlyCost = instances.reduce((sum, instance) => sum + instance.monthlyCost, 0);

  const formatCurrency = (amount: number) => 
    new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

  return (
    <Grid container spacing={3} className="overview-cards">
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Typography color="textSecondary" gutterBottom>
              Total P&L
            </Typography>
            <Box display="flex" alignItems="center">
              <Typography variant="h5" component="div">
                {formatCurrency(totalPnL)}
              </Typography>
              {totalPnL >= 0 ? (
                <TrendingUp color="success" />
              ) : (
                <TrendingDown color="error" />
              )}
            </Box>
            <Typography variant="body2" color={totalPnL >= 0 ? 'success.main' : 'error.main'}>
              {totalPnL >= 0 ? '+' : ''}{((totalPnL / 10000) * 100).toFixed(1)}%
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Typography color="textSecondary" gutterBottom>
              Active Instances
            </Typography>
            <Typography variant="h5" component="div">
              {activeInstances}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              of {instances.length} total
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Typography color="textSecondary" gutterBottom>
              Total Trades
            </Typography>
            <Typography variant="h5" component="div">
              {totalTrades.toLocaleString()}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              This month
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Typography color="textSecondary" gutterBottom>
              Monthly Cost
            </Typography>
            <Typography variant="h5" component="div">
              {formatCurrency(monthlyCost)}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Next billing: Jan 15
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

export default OverviewCards;
```

### Instance Grid Component

```typescript
// src/components/Dashboard/InstanceGrid.tsx
import React from 'react';
import { Grid, Card, CardContent, Typography, Button, Chip, Box } from '@mui/material';
import { PlayArrow, Stop, Analytics, Settings } from '@mui/icons-material';

interface InstanceGridProps {
  instances: Instance[];
  loading: boolean;
  onRefresh: () => void;
}

const InstanceGrid: React.FC<InstanceGridProps> = ({ instances, loading, onRefresh }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'stopped': return 'default';
      case 'error': return 'error';
      case 'creating': return 'warning';
      default: return 'default';
    }
  };

  const formatCurrency = (amount: number) => 
    new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

  return (
    <div className="instance-grid">
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6">My Trading Instances</Typography>
        <Button onClick={onRefresh} disabled={loading}>
          Refresh
        </Button>
      </Box>

      <Grid container spacing={3}>
        {instances.map((instance) => (
          <Grid item xs={12} md={6} lg={4} key={instance.id}>
            <Card className="instance-card">
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                  <div>
                    <Typography variant="h6" component="div">
                      {instance.name}
                    </Typography>
                    <Typography color="textSecondary">
                      {instance.broker}
                    </Typography>
                  </div>
                  <Chip 
                    label={instance.status} 
                    color={getStatusColor(instance.status)}
                    size="small"
                  />
                </Box>

                <Box mb={2}>
                  <Typography variant="body2" color="textSecondary">
                    Static IP: {instance.staticIp}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Plan: {instance.planType}
                  </Typography>
                </Box>

                <Box display="flex" justifyContent="space-between" mb={2}>
                  <div>
                    <Typography variant="body2" color="textSecondary">P&L</Typography>
                    <Typography 
                      variant="h6" 
                      color={instance.pnl >= 0 ? 'success.main' : 'error.main'}
                    >
                      {formatCurrency(instance.pnl)}
                    </Typography>
                  </div>
                  <div>
                    <Typography variant="body2" color="textSecondary">Trades</Typography>
                    <Typography variant="h6">
                      {instance.totalTrades}
                    </Typography>
                  </div>
                </Box>

                <Box display="flex" gap={1} flexWrap="wrap">
                  <Button
                    size="small"
                    variant="outlined"
                    startIcon={<Analytics />}
                    href={`/instances/${instance.id}/analytics`}
                  >
                    View
                  </Button>
                  <Button
                    size="small"
                    variant="outlined"
                    startIcon={<Settings />}
                    href={`/instances/${instance.id}/settings`}
                  >
                    Manage
                  </Button>
                  {instance.status === 'active' ? (
                    <Button
                      size="small"
                      variant="outlined"
                      color="error"
                      startIcon={<Stop />}
                      onClick={() => handleStopInstance(instance.id)}
                    >
                      Stop
                    </Button>
                  ) : (
                    <Button
                      size="small"
                      variant="outlined"
                      color="success"
                      startIcon={<PlayArrow />}
                      onClick={() => handleStartInstance(instance.id)}
                    >
                      Start
                    </Button>
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </div>
  );
};

export default InstanceGrid;
```

### Create Instance Modal

```typescript
// src/components/Dashboard/CreateInstanceModal.tsx
import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stepper,
  Step,
  StepLabel,
  Box
} from '@mui/material';

interface CreateInstanceModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const CreateInstanceModal: React.FC<CreateInstanceModalProps> = ({
  open,
  onClose,
  onSuccess
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [instanceData, setInstanceData] = useState({
    name: '',
    broker: '',
    planType: 'basic',
    credentials: {}
  });

  const steps = ['Instance Details', 'Broker Selection', 'Credentials', 'Review'];

  const brokers = [
    { value: 'zerodha', label: 'Zerodha' },
    { value: 'angel', label: 'Angel One' },
    { value: 'upstox', label: 'Upstox' },
    { value: 'dhan', label: 'Dhan' },
    { value: 'fyers', label: 'Fyers' }
  ];

  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleCreateInstance = async () => {
    try {
      // API call to create instance
      await createInstance(instanceData);
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Failed to create instance:', error);
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <TextField
              fullWidth
              label="Instance Name"
              value={instanceData.name}
              onChange={(e) => setInstanceData({...instanceData, name: e.target.value})}
              margin="normal"
            />
            <FormControl fullWidth margin="normal">
              <InputLabel>Plan Type</InputLabel>
              <Select
                value={instanceData.planType}
                onChange={(e) => setInstanceData({...instanceData, planType: e.target.value})}
              >
                <MenuItem value="basic">Basic - $29/month</MenuItem>
                <MenuItem value="pro">Pro - $49/month</MenuItem>
              </Select>
            </FormControl>
          </Box>
        );
      case 1:
        return (
          <FormControl fullWidth margin="normal">
            <InputLabel>Select Broker</InputLabel>
            <Select
              value={instanceData.broker}
              onChange={(e) => setInstanceData({...instanceData, broker: e.target.value})}
            >
              {brokers.map((broker) => (
                <MenuItem key={broker.value} value={broker.value}>
                  {broker.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );
      case 2:
        return <BrokerCredentialsForm broker={instanceData.broker} />;
      case 3:
        return <InstanceReview instanceData={instanceData} />;
      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Create New Trading Instance</DialogTitle>
      <DialogContent>
        <Stepper activeStep={activeStep} sx={{ mb: 3 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
        {renderStepContent(activeStep)}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleBack} disabled={activeStep === 0}>
          Back
        </Button>
        {activeStep === steps.length - 1 ? (
          <Button onClick={handleCreateInstance} variant="contained">
            Create Instance
          </Button>
        ) : (
          <Button onClick={handleNext} variant="contained">
            Next
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default CreateInstanceModal;
```

## Real-time Updates

### WebSocket Integration

```typescript
// src/hooks/useRealTimeData.ts
import { useEffect, useState } from 'react';
import { io, Socket } from 'socket.io-client';

interface RealTimeData {
  instanceId: string;
  trades: Trade[];
  positions: Position[];
  pnl: number;
  status: string;
}

export const useRealTimeData = (instanceIds: string[]) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [data, setData] = useState<Record<string, RealTimeData>>({});

  useEffect(() => {
    const newSocket = io('/ws', {
      auth: {
        token: localStorage.getItem('authToken')
      }
    });

    // Subscribe to instance updates
    instanceIds.forEach(instanceId => {
      newSocket.emit('subscribe', { instanceId });
    });

    // Listen for updates
    newSocket.on('instanceUpdate', (update: RealTimeData) => {
      setData(prev => ({
        ...prev,
        [update.instanceId]: update
      }));
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, [instanceIds]);

  return { data, socket };
};
```

## Mobile Responsive Design

### CSS Styles

```scss
// src/styles/Dashboard.scss
.dashboard-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  @media (max-width: 768px) {
    padding: 16px;
  }
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
}

.overview-cards {
  margin-bottom: 32px;

  .MuiCard-root {
    height: 100%;
    transition: transform 0.2s ease-in-out;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
  }
}

.instance-grid {
  .instance-card {
    height: 100%;
    transition: transform 0.2s ease-in-out;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
  }
}
```

This dashboard design provides a comprehensive, user-friendly interface for managing multiple trading instances while maintaining real-time visibility into performance and status.
