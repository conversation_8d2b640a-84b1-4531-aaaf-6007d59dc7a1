#!/usr/bin/env python3
"""
Simple test script to verify authentication system
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_auth_flow():
    """Test the complete authentication flow"""
    
    # Test data
    test_user = {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "first_name": "Test",
        "last_name": "User"
    }
    
    print("🧪 Testing AlgoFactory Authentication System")
    print("=" * 50)
    
    # 1. Test user registration
    print("\n1. Testing user registration...")
    try:
        response = requests.post(f"{BASE_URL}/auth/register", json=test_user)
        if response.status_code == 200:
            print("✅ Registration successful!")
            user_data = response.json()
            print(f"   User ID: {user_data['id']}")
            print(f"   Email: {user_data['email']}")
        else:
            print(f"❌ Registration failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return False
    
    # 2. Test user login
    print("\n2. Testing user login...")
    try:
        login_data = {
            "username": test_user["email"],
            "password": test_user["password"]
        }
        response = requests.post(
            f"{BASE_URL}/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        if response.status_code == 200:
            print("✅ Login successful!")
            token_data = response.json()
            access_token = token_data["access_token"]
            refresh_token = token_data["refresh_token"]
            print(f"   Token type: {token_data['token_type']}")
            print(f"   Access token: {access_token[:20]}...")
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # 3. Test protected endpoint (user profile)
    print("\n3. Testing protected endpoint...")
    try:
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(f"{BASE_URL}/users/profile", headers=headers)
        if response.status_code == 200:
            print("✅ Protected endpoint access successful!")
            profile_data = response.json()
            print(f"   Profile: {profile_data['email']}")
        else:
            print(f"❌ Protected endpoint failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Protected endpoint error: {e}")
        return False
    
    # 4. Test token refresh
    print("\n4. Testing token refresh...")
    try:
        refresh_data = {"refresh_token": refresh_token}
        response = requests.post(f"{BASE_URL}/auth/refresh", json=refresh_data)
        if response.status_code == 200:
            print("✅ Token refresh successful!")
            new_token_data = response.json()
            print(f"   New access token: {new_token_data['access_token'][:20]}...")
        else:
            print(f"❌ Token refresh failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Token refresh error: {e}")
        return False
    
    print("\n🎉 All authentication tests passed!")
    return True

def test_api_health():
    """Test if the API is running"""
    print("🏥 Testing API health...")
    try:
        response = requests.get("http://localhost:8000/health")
        if response.status_code == 200:
            print("✅ API is healthy!")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API health check error: {e}")
        print("   Make sure the backend server is running on localhost:8000")
        return False

if __name__ == "__main__":
    if test_api_health():
        test_auth_flow()
    else:
        print("\n💡 To start the backend server:")
        print("   cd backend")
        print("   uvicorn app.main:app --reload")
        print("\n   Or use Docker:")
        print("   docker-compose up -d")
