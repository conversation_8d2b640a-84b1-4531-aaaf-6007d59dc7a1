# AlgoFactory - Instance Management System

## Overview

The Instance Management System is the core component that handles the lifecycle of OpenAlgo instances on AWS Lightsail. It provides automated provisioning, configuration, monitoring, and management of trading instances.

## Instance Lifecycle

### 1. Instance Creation Flow

```mermaid
graph TD
    A[User Requests Instance] --> B[Validate User Subscription]
    B --> C[Validate Broker Credentials]
    C --> D[Create Lightsail Instance]
    D --> E[Assign Static IP]
    E --> F[Install OpenAlgo]
    F --> G[Configure Auto-User]
    G --> H[Setup Broker Connection]
    H --> I[Health Check]
    I --> J[Instance Ready]
    J --> K[Notify User]
```

### 2. Instance States

```python
INSTANCE_STATES = {
    'CREATING': 'Instance is being provisioned',
    'CONFIGURING': 'OpenAlgo is being installed and configured',
    'STARTING': 'Services are starting up',
    'ACTIVE': 'Instance is running and ready',
    'STOPPING': 'Instance is being stopped',
    'STOPPED': 'Instance is stopped but preserved',
    'ERROR': 'Instance encountered an error',
    'DELETING': 'Instance is being deleted',
    'DELETED': 'Instance has been removed'
}
```

## AWS Lightsail Integration

### Instance Provisioning Service

```python
import boto3
import time
import json
from typing import Dict, Optional, Tuple

class LightsailInstanceManager:
    def __init__(self):
        self.lightsail = boto3.client('lightsail', region_name='us-east-1')
        self.instance_configs = {
            'basic': {
                'bundle_id': 'nano_2_0',  # $5/month
                'blueprint_id': 'ubuntu_20_04'
            },
            'pro': {
                'bundle_id': 'micro_2_0',  # $10/month
                'blueprint_id': 'ubuntu_20_04'
            }
        }
    
    def create_instance(self, user_id: str, instance_name: str, 
                       plan_type: str = 'basic') -> Dict:
        """Create a new Lightsail instance"""
        
        # Generate unique instance name
        lightsail_name = f"algofactory-{user_id[:8]}-{instance_name}"
        
        # User data script for OpenAlgo installation
        user_data = self._generate_user_data_script(user_id, instance_name)
        
        try:
            # Create instance
            response = self.lightsail.create_instances(
                instanceNames=[lightsail_name],
                availabilityZone='us-east-1a',
                bundleId=self.instance_configs[plan_type]['bundle_id'],
                blueprintId=self.instance_configs[plan_type]['blueprint_id'],
                userData=user_data,
                tags=[
                    {'key': 'Project', 'value': 'AlgoFactory'},
                    {'key': 'UserId', 'value': user_id},
                    {'key': 'InstanceName', 'value': instance_name}
                ]
            )
            
            # Wait for instance to be running
            self._wait_for_instance_running(lightsail_name)
            
            # Allocate and attach static IP
            static_ip = self._allocate_static_ip(lightsail_name)
            
            return {
                'lightsail_name': lightsail_name,
                'static_ip': static_ip,
                'status': 'CREATING'
            }
            
        except Exception as e:
            raise Exception(f"Failed to create instance: {str(e)}")
    
    def _generate_user_data_script(self, user_id: str, instance_name: str) -> str:
        """Generate the user data script for instance initialization"""
        
        script = f"""#!/bin/bash
# AlgoFactory Instance Setup Script
set -e

# Log all output
exec > >(tee /var/log/algofactory-setup.log)
exec 2>&1

echo "Starting AlgoFactory instance setup..."

# Update system
apt-get update
apt-get upgrade -y

# Install required packages
apt-get install -y curl git python3 python3-pip docker.io docker-compose

# Enable Docker
systemctl enable docker
systemctl start docker

# Add ubuntu user to docker group
usermod -aG docker ubuntu

# Create application directory
mkdir -p /opt/algofactory
cd /opt/algofactory

# Download OpenAlgo
git clone https://github.com/marketcalls/openalgo.git openalgo
cd openalgo

# Generate secure keys
APP_KEY=$(openssl rand -hex 32)
API_KEY_PEPPER=$(openssl rand -hex 32)

# Create environment file
cat > .env << EOF
DATABASE_URL=sqlite:///db/openalgo.db
APP_KEY=$APP_KEY
API_KEY_PEPPER=$API_KEY_PEPPER
FLASK_HOST_IP=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=False
HOST_SERVER=http://\$(curl -s http://***************/latest/meta-data/public-ipv4):5000
CSRF_ENABLED=TRUE
SESSION_EXPIRY_TIME=03:30
RATE_LIMIT_ENABLED=TRUE
EOF

# Create auto-setup script
cat > auto_setup.py << 'PYTHON_SCRIPT'
import os
import sys
import time
import requests
import secrets
import string

def generate_password(length=16):
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def wait_for_openalgo():
    max_attempts = 30
    for attempt in range(max_attempts):
        try:
            response = requests.get('http://localhost:5000/', timeout=5)
            if response.status_code == 200:
                return True
        except:
            pass
        time.sleep(10)
    return False

def setup_user():
    # Generate credentials
    username = "autouser"
    password = generate_password()
    email = "<EMAIL>"
    
    # Setup user
    setup_data = {{
        'username': username,
        'email': email,
        'password': password
    }}
    
    try:
        response = requests.post('http://localhost:5000/setup', data=setup_data)
        if response.status_code in [200, 302]:
            # Save credentials for AlgoFactory
            with open('/opt/algofactory/credentials.json', 'w') as f:
                import json
                json.dump({{
                    'username': username,
                    'password': password,
                    'email': email
                }}, f)
            return True
    except Exception as e:
        print(f"Setup failed: {{e}}")
    return False

if __name__ == "__main__":
    print("Waiting for OpenAlgo to start...")
    if wait_for_openalgo():
        print("Setting up auto user...")
        if setup_user():
            print("Auto setup completed successfully")
        else:
            print("Auto setup failed")
    else:
        print("OpenAlgo failed to start")
PYTHON_SCRIPT

# Start OpenAlgo
docker-compose up -d

# Wait and setup auto user
sleep 30
python3 auto_setup.py

# Create status file
echo "READY" > /opt/algofactory/status

# Setup health check endpoint
cat > /opt/algofactory/health_check.py << 'HEALTH_SCRIPT'
from flask import Flask, jsonify
import json
import os

app = Flask(__name__)

@app.route('/health')
def health():
    try:
        # Check if OpenAlgo is running
        import requests
        response = requests.get('http://localhost:5000/', timeout=5)
        
        # Check if credentials exist
        creds_exist = os.path.exists('/opt/algofactory/credentials.json')
        
        return jsonify({{
            'status': 'healthy' if response.status_code == 200 and creds_exist else 'unhealthy',
            'openalgo_status': response.status_code,
            'credentials_ready': creds_exist
        }})
    except Exception as e:
        return jsonify({{'status': 'unhealthy', 'error': str(e)}}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080)
HEALTH_SCRIPT

# Start health check service
nohup python3 /opt/algofactory/health_check.py > /var/log/health_check.log 2>&1 &

echo "AlgoFactory instance setup completed"
"""
        return script
    
    def _wait_for_instance_running(self, instance_name: str, timeout: int = 300):
        """Wait for instance to be in running state"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            response = self.lightsail.get_instance(instanceName=instance_name)
            state = response['instance']['state']['name']
            
            if state == 'running':
                return True
            elif state == 'terminated':
                raise Exception("Instance terminated during creation")
            
            time.sleep(10)
        
        raise Exception("Instance creation timeout")
    
    def _allocate_static_ip(self, instance_name: str) -> str:
        """Allocate and attach static IP to instance"""
        static_ip_name = f"{instance_name}-static-ip"
        
        # Allocate static IP
        self.lightsail.allocate_static_ip(staticIpName=static_ip_name)
        
        # Attach to instance
        self.lightsail.attach_static_ip(
            staticIpName=static_ip_name,
            instanceName=instance_name
        )
        
        # Get the IP address
        response = self.lightsail.get_static_ip(staticIpName=static_ip_name)
        return response['staticIp']['ipAddress']
    
    def delete_instance(self, instance_name: str, static_ip_name: str):
        """Delete instance and associated resources"""
        try:
            # Detach and release static IP
            self.lightsail.detach_static_ip(staticIpName=static_ip_name)
            self.lightsail.release_static_ip(staticIpName=static_ip_name)
            
            # Delete instance
            self.lightsail.delete_instance(instanceName=instance_name)
            
        except Exception as e:
            raise Exception(f"Failed to delete instance: {str(e)}")
```

### Instance Health Monitoring

```python
class InstanceHealthMonitor:
    def __init__(self):
        self.health_check_interval = 60  # seconds
        
    async def check_instance_health(self, instance_id: str, static_ip: str) -> Dict:
        """Check health of an OpenAlgo instance"""
        
        health_data = {
            'instance_id': instance_id,
            'timestamp': time.time(),
            'status': 'unknown',
            'checks': {}
        }
        
        try:
            # Check health endpoint
            health_response = await self._check_health_endpoint(static_ip)
            health_data['checks']['health_endpoint'] = health_response
            
            # Check OpenAlgo API
            api_response = await self._check_openalgo_api(static_ip)
            health_data['checks']['openalgo_api'] = api_response
            
            # Check system resources
            system_response = await self._check_system_resources(static_ip)
            health_data['checks']['system_resources'] = system_response
            
            # Determine overall status
            if all(check.get('status') == 'healthy' for check in health_data['checks'].values()):
                health_data['status'] = 'healthy'
            else:
                health_data['status'] = 'unhealthy'
                
        except Exception as e:
            health_data['status'] = 'error'
            health_data['error'] = str(e)
        
        return health_data
    
    async def _check_health_endpoint(self, static_ip: str) -> Dict:
        """Check custom health endpoint"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f'http://{static_ip}:8080/health', timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {'status': 'healthy', 'data': data}
                    else:
                        return {'status': 'unhealthy', 'http_status': response.status}
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    async def _check_openalgo_api(self, static_ip: str) -> Dict:
        """Check OpenAlgo API responsiveness"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f'http://{static_ip}:5000/', timeout=10) as response:
                    return {
                        'status': 'healthy' if response.status == 200 else 'unhealthy',
                        'http_status': response.status,
                        'response_time': response.headers.get('X-Response-Time', 'unknown')
                    }
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
```

## Instance Configuration Management

### Broker Credential Management

```python
from cryptography.fernet import Fernet
import json
import base64

class BrokerCredentialManager:
    def __init__(self, encryption_key: str):
        self.fernet = Fernet(encryption_key.encode())
    
    def encrypt_credentials(self, credentials: Dict) -> str:
        """Encrypt broker credentials"""
        json_data = json.dumps(credentials)
        encrypted_data = self.fernet.encrypt(json_data.encode())
        return base64.b64encode(encrypted_data).decode()
    
    def decrypt_credentials(self, encrypted_credentials: str) -> Dict:
        """Decrypt broker credentials"""
        encrypted_data = base64.b64decode(encrypted_credentials.encode())
        decrypted_data = self.fernet.decrypt(encrypted_data)
        return json.loads(decrypted_data.decode())
    
    async def configure_broker_in_instance(self, instance_id: str, 
                                         broker: str, credentials: Dict):
        """Configure broker credentials in OpenAlgo instance"""
        
        # Get instance details
        instance = await self.get_instance(instance_id)
        
        # Get auto-user credentials
        auto_creds = await self._get_auto_user_credentials(instance.static_ip)
        
        # Login to OpenAlgo instance
        session = await self._login_to_instance(instance.static_ip, auto_creds)
        
        # Configure broker
        await self._setup_broker_connection(session, broker, credentials)
        
        return True
    
    async def _setup_broker_connection(self, session, broker: str, credentials: Dict):
        """Setup broker connection in OpenAlgo instance"""
        
        broker_config_map = {
            'zerodha': self._setup_zerodha,
            'angel': self._setup_angel,
            'upstox': self._setup_upstox,
            'dhan': self._setup_dhan,
            # Add more brokers as needed
        }
        
        if broker in broker_config_map:
            await broker_config_map[broker](session, credentials)
        else:
            raise ValueError(f"Unsupported broker: {broker}")
```

## Instance Scaling and Management

### Auto-scaling Logic

```python
class InstanceScaler:
    def __init__(self):
        self.scaling_metrics = {
            'cpu_threshold': 80,  # %
            'memory_threshold': 85,  # %
            'api_response_time': 1000,  # ms
            'error_rate': 5  # %
        }
    
    async def evaluate_scaling_needs(self, user_id: str):
        """Evaluate if user needs instance scaling"""
        
        user_instances = await self.get_user_instances(user_id)
        scaling_recommendations = []
        
        for instance in user_instances:
            metrics = await self.get_instance_metrics(instance.id)
            
            if self._should_scale_up(metrics):
                scaling_recommendations.append({
                    'instance_id': instance.id,
                    'action': 'scale_up',
                    'reason': 'High resource utilization',
                    'current_plan': instance.plan_type,
                    'recommended_plan': self._get_next_plan(instance.plan_type)
                })
            
            elif self._should_scale_down(metrics):
                scaling_recommendations.append({
                    'instance_id': instance.id,
                    'action': 'scale_down',
                    'reason': 'Low resource utilization',
                    'current_plan': instance.plan_type,
                    'recommended_plan': self._get_previous_plan(instance.plan_type)
                })
        
        return scaling_recommendations
```

This instance management system provides comprehensive automation for OpenAlgo instances while maintaining security, scalability, and reliability.
