# AlgoFactory - Multi-Tenant Trading Platform

AlgoFactory is a SaaS platform that provides automated trading infrastructure to users through OpenAlgo instances on AWS Lightsail.

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for local frontend development)
- Python 3.11+ (for local backend development)
- PostgreSQL (or use Docker)
- Redis (or use Docker)

### Local Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd algofactory
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start with Docker Compose**
   ```bash
   docker-compose up -d
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

### Manual Setup (Alternative)

#### Backend Setup
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn app.main:app --reload
```

#### Frontend Setup
```bash
cd frontend
npm install
npm start
```

## 📁 Project Structure

```
algofactory/
├── docs/                          # Project documentation
├── backend/                       # FastAPI backend
│   ├── app/
│   │   ├── api/                  # API routes
│   │   ├── core/                 # Core configuration
│   │   ├── models/               # Database models
│   │   ├── services/             # Business logic
│   │   └── utils/                # Utility functions
│   ├── tests/                    # Backend tests
│   ├── requirements.txt          # Python dependencies
│   └── Dockerfile               # Backend container
├── frontend/                     # React frontend
│   ├── src/
│   │   ├── components/          # React components
│   │   ├── pages/               # Page components
│   │   ├── services/            # API services
│   │   ├── store/               # Redux store
│   │   └── utils/               # Utility functions
│   ├── public/                  # Static assets
│   ├── package.json             # Node dependencies
│   └── Dockerfile              # Frontend container
├── infrastructure/              # AWS infrastructure code
├── docker-compose.yml          # Local development setup
├── .env.example               # Environment variables template
└── DEVELOPMENT_PROGRESS.md    # Development progress tracker
```

## 🛠️ Development

### Backend Development
- **Framework**: FastAPI with Python 3.11
- **Database**: PostgreSQL with SQLAlchemy
- **Authentication**: JWT tokens
- **API Documentation**: Automatic with FastAPI/Swagger

### Frontend Development
- **Framework**: React 18 with TypeScript
- **UI Library**: Material-UI (MUI)
- **State Management**: Redux Toolkit
- **Routing**: React Router v6

### Database
- **Primary**: PostgreSQL for main application data
- **Cache**: Redis for sessions and caching
- **Migrations**: Alembic for database migrations

## 🔧 Available Scripts

### Backend
```bash
# Start development server
uvicorn app.main:app --reload

# Run tests
pytest

# Format code
black .
isort .

# Lint code
flake8
mypy .
```

### Frontend
```bash
# Start development server
npm start

# Build for production
npm run build

# Run tests
npm test

# Lint and format
npm run lint
npm run format
```

## 📊 Current Development Status

See [DEVELOPMENT_PROGRESS.md](./DEVELOPMENT_PROGRESS.md) for detailed progress tracking.

**Phase 1: Foundation (Current)**
- [x] Project structure setup
- [x] Basic backend API structure
- [x] Basic frontend structure
- [x] Docker development environment
- [ ] Database models and migrations
- [ ] Authentication system
- [ ] Basic UI components

## 🏗️ Architecture

### System Overview
- **Frontend**: React SPA with Material-UI
- **Backend**: FastAPI with async support
- **Database**: PostgreSQL for persistence
- **Cache**: Redis for sessions and real-time data
- **Infrastructure**: AWS Lightsail for OpenAlgo instances

### Key Features (Planned)
- Multi-tenant user management
- Automated OpenAlgo instance provisioning
- Real-time trading data and monitoring
- Multi-broker support (20+ brokers)
- Subscription and billing management
- RESTful API with WebSocket support

## 🔐 Security

- JWT-based authentication
- Role-based access control
- Encrypted broker credentials
- TLS encryption for all communications
- Rate limiting and request throttling

## 📈 Monitoring

- Application metrics with Prometheus
- Structured logging
- Health check endpoints
- Error tracking and alerting

## 🚀 Deployment

### Development
- Docker Compose for local development
- Hot reloading for both frontend and backend

### Production (Planned)
- AWS infrastructure with Terraform
- Container orchestration
- Load balancing and auto-scaling
- Monitoring and alerting

## 📝 API Documentation

Once the backend is running, visit:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🤝 Contributing

1. Follow the development roadmap in `docs/07_development_roadmap.md`
2. Update progress in `DEVELOPMENT_PROGRESS.md`
3. Write tests for new features
4. Follow code formatting standards
5. Update documentation as needed

## 📞 Support

For development questions and issues, refer to:
- [Technical Architecture](docs/02_technical_architecture.md)
- [Development Roadmap](docs/07_development_roadmap.md)
- [Development Progress](DEVELOPMENT_PROGRESS.md)

---

**Status**: 🚧 Under Development - Phase 1 Foundation
**Last Updated**: [Current Date]
