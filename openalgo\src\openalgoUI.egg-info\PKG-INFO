Metadata-Version: 2.4
Name: openalgoUI
Version: 1.0.0.29
Summary: Broker-agnostic open-source trading automation
Requires-Python: >=3.12
License-File: License.md
Requires-Dist: annotated-types==0.7.0
Requires-Dist: aniso8601==9.0.1
Requires-Dist: anyio==4.8.0
Requires-Dist: APScheduler==3.11.0
Requires-Dist: argon2-cffi==23.1.0
Requires-Dist: argon2-cffi-bindings==21.2.0
Requires-Dist: asttokens==3.0.0
Requires-Dist: attrs==24.2.0
Requires-Dist: bcrypt==4.1.3
Requires-Dist: bidict==0.23.1
Requires-Dist: blinker==1.8.2
Requires-Dist: cachetools==5.3.3
Requires-Dist: certifi==2024.7.4
Requires-Dist: cffi==1.17.1
Requires-Dist: charset-normalizer==3.3.2
Requires-Dist: click==8.1.7
Requires-Dist: colorama==0.4.6
Requires-Dist: comm==0.2.2
Requires-Dist: cryptography==44.0.1
Requires-Dist: darkdetect==0.8.0
Requires-Dist: debugpy==1.8.13
Requires-Dist: decorator==5.2.1
Requires-Dist: Deprecated==1.2.14
Requires-Dist: dnspython==2.6.1
Requires-Dist: duckdb==1.2.2
Requires-Dist: email-validator==2.1.1
Requires-Dist: executing==2.2.0
Requires-Dist: Flask==3.0.3
Requires-Dist: Flask-Bcrypt==1.0.1
Requires-Dist: Flask-Cors==6.0.0
Requires-Dist: Flask-Limiter==3.7.0
Requires-Dist: Flask-Login==0.6.3
Requires-Dist: flask-restx==1.3.0
Requires-Dist: Flask-SocketIO==5.3.6
Requires-Dist: Flask-SQLAlchemy==3.1.1
Requires-Dist: Flask-WTF==1.2.1
Requires-Dist: greenlet==3.1.1
Requires-Dist: h11==0.16.0
Requires-Dist: h2==4.2.0
Requires-Dist: hpack==4.1.0
Requires-Dist: httpcore==1.0.9
Requires-Dist: httpx[http2]==0.28.1
Requires-Dist: httpx-sse==0.4.1
Requires-Dist: hyperframe==6.1.0
Requires-Dist: idna==3.7
Requires-Dist: importlib-resources==6.4.0
Requires-Dist: ipykernel==6.29.5
Requires-Dist: ipython==9.0.2
Requires-Dist: ipython-pygments-lexers==1.1.1
Requires-Dist: itsdangerous==2.2.0
Requires-Dist: jedi==0.19.2
Requires-Dist: Jinja2==3.1.6
Requires-Dist: jsonschema==4.23.0
Requires-Dist: jsonschema-specifications==2023.12.1
Requires-Dist: jupyter-client==8.6.3
Requires-Dist: jupyter-core==5.8.1
Requires-Dist: limits==3.13.0
Requires-Dist: logzero==1.7.0
Requires-Dist: markdown-it-py==3.0.0
Requires-Dist: MarkupSafe==2.1.5
Requires-Dist: marshmallow==3.22.0
Requires-Dist: matplotlib-inline==0.1.7
Requires-Dist: mcp==1.11.0
Requires-Dist: mdurl==0.1.2
Requires-Dist: nest-asyncio==1.6.0
Requires-Dist: numpy==2.2.4
Requires-Dist: openalgo==1.0.21
Requires-Dist: ordered-set==4.1.0
Requires-Dist: packaging==24.1
Requires-Dist: pandas==2.2.3
Requires-Dist: pandas-ta==0.3.14b0
Requires-Dist: parso==0.8.4
Requires-Dist: pillow==11.0.0
Requires-Dist: platformdirs==4.3.7
Requires-Dist: prompt-toolkit==3.0.50
Requires-Dist: protobuf==6.31.1
Requires-Dist: psutil==7.0.0
Requires-Dist: pure-eval==0.2.3
Requires-Dist: pycparser==2.22
Requires-Dist: pydantic==2.11.7
Requires-Dist: pydantic-core==2.33.2
Requires-Dist: pydantic-settings==2.10.1
Requires-Dist: Pygments==2.18.0
Requires-Dist: PyJWT==2.8.0
Requires-Dist: pyngrok==7.1.6
Requires-Dist: pyotp==2.9.0
Requires-Dist: python-dateutil==2.9.0.post0
Requires-Dist: python-dotenv==1.0.1
Requires-Dist: python-engineio==4.9.1
Requires-Dist: python-multipart==0.0.20
Requires-Dist: python-socketio==5.11.3
Requires-Dist: pytz==2024.1
Requires-Dist: PyYAML==6.0.1
Requires-Dist: pyzmq==26.3.0
Requires-Dist: qrcode==8.0
Requires-Dist: referencing==0.35.1
Requires-Dist: requests==2.32.4
Requires-Dist: rich==13.7.1
Requires-Dist: rpds-py==0.20.0
Requires-Dist: setuptools==80.3.1
Requires-Dist: simple-websocket==1.0.0
Requires-Dist: six==1.16.0
Requires-Dist: sniffio==1.3.1
Requires-Dist: SQLAlchemy==2.0.31
Requires-Dist: sse-starlette==2.4.1
Requires-Dist: stack-data==0.6.3
Requires-Dist: starlette==0.47.1
Requires-Dist: tornado==6.5.0
Requires-Dist: traitlets==5.14.3
Requires-Dist: typing-extensions==4.12.2
Requires-Dist: typing-inspection==0.4.1
Requires-Dist: tzdata==2024.1
Requires-Dist: tzlocal==5.2
Requires-Dist: urllib3==2.5.0
Requires-Dist: uvicorn==0.35.0
Requires-Dist: wcwidth==0.2.13
Requires-Dist: websocket-client==1.8.0
Requires-Dist: websockets==15.0.1
Requires-Dist: Werkzeug==3.1.2
Requires-Dist: wheel==0.43.0
Requires-Dist: wrapt==1.16.0
Requires-Dist: wsproto==1.2.0
Requires-Dist: WTForms==3.1.2
Requires-Dist: zipp==3.19.2
Requires-Dist: zmq==0.0.0
Dynamic: license-file
