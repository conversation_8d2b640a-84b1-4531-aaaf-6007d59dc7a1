# AlgoFactory Environment Variables

# Application
PROJECT_NAME=AlgoFactory
ENVIRONMENT=development
DEBUG=true

# Security
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Database
DATABASE_URL=postgresql://algofactory:password@localhost:5432/algofactory

# Redis
REDIS_URL=redis://localhost:6379/0

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# Lightsail Configuration
LIGHTSAIL_BLUEPRINT_ID=ubuntu_20_04
LIGHTSAIL_BUNDLE_ID=nano_2_0

# Email Configuration
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=AlgoFactory

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# Instance Configuration
MAX_INSTANCES_PER_USER=10
INSTANCE_CREATION_TIMEOUT=300

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8000
