{"name": "algofactory-frontend", "version": "1.0.0", "description": "AlgoFactory Frontend - Multi-tenant trading platform", "private": true, "dependencies": {"@reduxjs/toolkit": "^1.9.7", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.1.3", "react-router-dom": "^6.18.0", "react-scripts": "5.0.1", "typescript": "^5.2.2", "web-vitals": "^3.5.0", "axios": "^1.6.0", "@mui/material": "^5.14.18", "@mui/icons-material": "^5.14.18", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/x-data-grid": "^6.18.1", "@mui/x-date-pickers": "^6.18.1", "recharts": "^2.8.0", "socket.io-client": "^4.7.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "eslint": "^8.53.0", "prettier": "^3.1.0"}, "proxy": "http://localhost:8000"}