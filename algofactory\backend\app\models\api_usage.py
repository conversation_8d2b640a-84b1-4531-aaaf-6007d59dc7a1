"""
API Usage tracking model
"""

from sqlalchemy import Column, String, DateTime, Foreign<PERSON>ey, Integer, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
import uuid

from app.core.database import Base


class ApiUsage(Base):
    __tablename__ = "api_usage"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    instance_id = Column(UUID(as_uuid=True), ForeignKey("trading_instances.id"), nullable=True)
    endpoint = Column(String(255))
    method = Column(String(10))
    status_code = Column(Integer)
    response_time_ms = Column(Integer)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user = relationship("User", back_populates="api_usage")
    instance = relationship("TradingInstance", back_populates="api_usage")

    def __repr__(self):
        return f"<ApiUsage(id={self.id}, endpoint={self.endpoint}, status={self.status_code})>"
