import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
} from '@mui/material';
import { Add as AddIcon, Computer as ComputerIcon } from '@mui/icons-material';

const Instances: React.FC = () => {
  // Mock data - will be replaced with real data from Redux store
  const instances = [
    // Empty for now
  ];

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Trading Instances
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => {/* TODO: Open create instance dialog */}}
        >
          Create Instance
        </Button>
      </Box>

      {instances.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 8 }}>
            <ComputerIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              No Trading Instances
            </Typography>
            <Typography color="textSecondary" paragraph>
              Create your first trading instance to start automated trading with OpenAlgo.
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => {/* TODO: Open create instance dialog */}}
            >
              Create Your First Instance
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Grid container spacing={3}>
          {instances.map((instance: any) => (
            <Grid item xs={12} md={6} lg={4} key={instance.id}>
              <Card>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="start" mb={2}>
                    <Typography variant="h6">
                      {instance.name}
                    </Typography>
                    <Chip
                      label={instance.status}
                      color={
                        instance.status === 'active' ? 'success' :
                        instance.status === 'creating' ? 'warning' :
                        instance.status === 'stopped' ? 'default' : 'error'
                      }
                      size="small"
                    />
                  </Box>
                  <Typography color="textSecondary" gutterBottom>
                    Broker: {instance.broker}
                  </Typography>
                  {instance.staticIp && (
                    <Typography variant="body2" color="textSecondary">
                      IP: {instance.staticIp}
                    </Typography>
                  )}
                  <Box mt={2}>
                    <Button size="small" sx={{ mr: 1 }}>
                      View Details
                    </Button>
                    <Button size="small" color="secondary">
                      Manage
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );
};

export default Instances;
