# AlgoFactory Testing Guide

## 🚀 Quick Start Testing

### Prerequisites
- <PERSON><PERSON> and <PERSON><PERSON> Compose installed
- Python 3.11+ (for running test scripts)
- Node.js 18+ (for frontend development)

### 1. Start the Development Environment

```bash
# Copy environment variables
cp .env.example .env

# Start all services
docker-compose up -d

# Or use the startup script
# Windows:
start-dev.bat

# Linux/Mac:
chmod +x start-dev.sh
./start-dev.sh
```

### 2. Verify Services are Running

Check that all services are up:
```bash
docker-compose ps
```

You should see:
- `algofactory_postgres` - Database
- `algofactory_redis` - Cache
- `algofactory_backend` - API server
- `algofactory_frontend` - React app

### 3. Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Database**: localhost:5432 (user: algofactory, password: password)

## 🧪 Testing Authentication System

### Automated Testing

Run the authentication test script:
```bash
cd backend
python test_auth.py
```

This will test:
- User registration
- User login
- Protected endpoint access
- Token refresh

### Manual Testing via API Documentation

1. Go to http://localhost:8000/docs
2. Test the following endpoints:

#### Register a User
```json
POST /api/v1/auth/register
{
  "email": "<EMAIL>",
  "password": "testpassword123",
  "first_name": "Test",
  "last_name": "User"
}
```

#### Login
```json
POST /api/v1/auth/login
Form data:
- username: <EMAIL>
- password: testpassword123
```

#### Get Profile (Protected)
```
GET /api/v1/users/profile
Authorization: Bearer <your_access_token>
```

### Manual Testing via Frontend

1. Go to http://localhost:3000
2. Click "Sign Up" to register a new account
3. Fill in the registration form
4. After registration, go to login page
5. Login with your credentials
6. You should be redirected to the dashboard

## 🔧 Development Testing

### Backend Development

```bash
# Install dependencies
cd backend
pip install -r requirements.txt

# Run backend directly (without Docker)
uvicorn app.main:app --reload

# Run with different port
uvicorn app.main:app --reload --port 8001
```

### Frontend Development

```bash
# Install dependencies
cd frontend
npm install

# Run frontend directly (without Docker)
npm start

# Run tests
npm test
```

### Database Testing

Connect to PostgreSQL:
```bash
# Using Docker
docker exec -it algofactory_postgres psql -U algofactory -d algofactory

# Or directly if PostgreSQL is installed locally
psql -h localhost -U algofactory -d algofactory
```

Common queries:
```sql
-- List all users
SELECT id, email, first_name, last_name, created_at FROM users;

-- Check user count
SELECT COUNT(*) FROM users;

-- List all tables
\dt
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Check what's using the port
netstat -tulpn | grep :8000

# Kill the process
kill -9 <process_id>
```

#### 2. Database Connection Issues
```bash
# Check if PostgreSQL container is running
docker logs algofactory_postgres

# Restart the database
docker-compose restart postgres
```

#### 3. Frontend Build Issues
```bash
# Clear node modules and reinstall
cd frontend
rm -rf node_modules package-lock.json
npm install
```

#### 4. Backend Import Issues
```bash
# Make sure you're in the backend directory
cd backend

# Check Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

### Logs and Debugging

```bash
# View all logs
docker-compose logs

# View specific service logs
docker-compose logs backend
docker-compose logs frontend
docker-compose logs postgres

# Follow logs in real-time
docker-compose logs -f backend
```

## 📊 Testing Checklist

### ✅ Authentication Flow
- [ ] User can register with valid email/password
- [ ] User cannot register with duplicate email
- [ ] User can login with correct credentials
- [ ] User cannot login with incorrect credentials
- [ ] User receives JWT tokens on successful login
- [ ] Protected endpoints require valid token
- [ ] Token refresh works correctly
- [ ] User can access profile after login

### ✅ Frontend Integration
- [ ] Registration form validates input
- [ ] Login form handles success/error states
- [ ] User is redirected after successful login
- [ ] Protected routes require authentication
- [ ] User can logout and tokens are cleared
- [ ] API errors are displayed to user

### ✅ Database Operations
- [ ] User data is stored correctly
- [ ] Passwords are hashed (not stored in plain text)
- [ ] Database constraints work (unique email)
- [ ] User profile updates work
- [ ] Database relationships are properly defined

## 🔄 Next Testing Steps

After authentication is working:

1. **Instance Management Testing**
   - Create trading instance endpoints
   - Test instance CRUD operations
   - Verify user can only access their instances

2. **AWS Integration Testing**
   - Test Lightsail instance creation
   - Verify static IP allocation
   - Test instance health monitoring

3. **End-to-End Testing**
   - Complete user journey testing
   - Performance testing
   - Security testing

## 📞 Getting Help

If you encounter issues:

1. Check the logs: `docker-compose logs`
2. Verify all services are running: `docker-compose ps`
3. Check the API documentation: http://localhost:8000/docs
4. Review the development progress: `DEVELOPMENT_PROGRESS.md`

---

**Happy Testing! 🎉**
