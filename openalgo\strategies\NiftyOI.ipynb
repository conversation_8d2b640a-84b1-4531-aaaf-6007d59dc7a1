{"cells": [{"cell_type": "code", "execution_count": null, "id": "70aa6cb5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔁 OpenAlgo Python <PERSON> is running.\n", "Underlying Quote : {'data': {'ask': 0.0, 'bid': 0.0, 'high': 25654.2, 'low': 25523.55, 'ltp': 25637.8, 'oi': 0, 'open': 25576.65, 'prev_close': 25549.0, 'volume': 0}, 'status': 'success'}\n", "NIFTY31JUL2523800CE → {'status': 'error', 'message': 'HTTP 429: {\"message\": \"10 per 1 second\"}\\n', 'code': 429, 'error_type': 'http_error'}\n", "NIFTY31JUL2523900PE → {'data': {'ask': 29.5, 'bid': 28.2, 'high': 40.65, 'low': 28.0, 'ltp': 28.8, 'oi': 503025, 'open': 35.85, 'prev_close': 39.1, 'volume': 478800}, 'status': 'success'}\n", "NIFTY31JUL2523600PE → {'data': {'ask': 26.55, 'bid': 20.6, 'high': 33.1, 'low': 20.4, 'ltp': 20.85, 'oi': 205125, 'open': 26.6, 'prev_close': 27.25, 'volume': 594000}, 'status': 'success'}\n", "NIFTY31JUL2524000PE → {'data': {'ask': 33.3, 'bid': 33.0, 'high': 44.35, 'low': 32.45, 'ltp': 33.2, 'oi': 3414675, 'open': 43.0, 'prev_close': 44.4, 'volume': 3480150}, 'status': 'success'}\n", "NIFTY31JUL2523700CE → {'data': {'ask': 2122.25, 'bid': 2046.55, 'high': 2061.0, 'low': 1992.35, 'ltp': 2051.0, 'oi': 8175, 'open': 1994.9, 'prev_close': 1911.0, 'volume': 1200}, 'status': 'success'}\n", "NIFTY31JUL2523900CE → {'data': {'ask': 1884.45, 'bid': 1857.65, 'high': 1891.5, 'low': 1851.7, 'ltp': 1891.5, 'oi': 30075, 'open': 1851.7, 'prev_close': 1706.0, 'volume': 150}, 'status': 'success'}\n", "NIFTY31JUL2523800PE → {'data': {'ask': 25.55, 'bid': 24.9, 'high': 36.6, 'low': 24.35, 'ltp': 25.0, 'oi': 691500, 'open': 29.95, 'prev_close': 33.7, 'volume': 996000}, 'status': 'success'}\n", "NIFTY31JUL2524000CE → {'data': {'ask': 1773.0, 'bid': 1770.6, 'high': 1785.0, 'low': 1670.0, 'ltp': 1768.1, 'oi': 916500, 'open': 1740.0, 'prev_close': 1642.95, 'volume': 96975}, 'status': 'success'}\n", "NIFTY31JUL2523700PE → {'data': {'ask': 25.25, 'bid': 22.65, 'high': 34.65, 'low': 22.45, 'ltp': 22.8, 'oi': 342450, 'open': 26.95, 'prev_close': 30.0, 'volume': 862125}, 'status': 'success'}\n", "NIFTY31JUL2523600CE → {'data': {'ask': 2185.5, 'bid': 2147.25, 'high': 2170.6, 'low': 2127.75, 'ltp': 2149.4, 'oi': 41325, 'open': 2144.85, 'prev_close': 2025.3, 'volume': 3450}, 'status': 'success'}\n", "NIFTY31JUL2523800CE → {'data': {'ask': 1979.75, 'bid': 1954.95, 'high': 1943.3, 'low': 1854.5, 'ltp': 1943.3, 'oi': 31950, 'open': 1915.75, 'prev_close': 1832.4, 'volume': 1125}, 'status': 'success'}\n", "NIFTY31JUL2524100CE → {'data': {'ask': 1770.95, 'bid': 1660.05, 'high': 1648.65, 'low': 1587.0, 'ltp': 1648.65, 'oi': 19875, 'open': 1609.1, 'prev_close': 1545.0, 'volume': 6075}, 'status': 'success'}\n", "NIFTY31JUL2524100PE → {'data': {'ask': 39.35, 'bid': 36.5, 'high': 47.5, 'low': 36.0, 'ltp': 36.75, 'oi': 265125, 'open': 45.0, 'prev_close': 49.25, 'volume': 490200}, 'status': 'success'}\n", "NIFTY31JUL2524400CE → {'data': {'ask': 1417.25, 'bid': 1388.0, 'high': 1403.1, 'low': 1304.0, 'ltp': 1399.0, 'oi': 50550, 'open': 1351.65, 'prev_close': 1277.2, 'volume': 6750}, 'status': 'success'}\n", "NIFTY31JUL2524300PE → {'data': {'ask': 48.0, 'bid': 47.05, 'high': 62.2, 'low': 46.65, 'ltp': 47.45, 'oi': 387675, 'open': 59.4, 'prev_close': 64.5, 'volume': 656850}, 'status': 'success'}\n", "NIFTY31JUL2524200PE → {'data': {'ask': 42.5, 'bid': 41.55, 'high': 54.3, 'low': 41.1, 'ltp': 41.9, 'oi': 590850, 'open': 50.15, 'prev_close': 56.4, 'volume': 789825}, 'status': 'success'}\n", "NIFTY31JUL2524500PE → {'data': {'ask': 62.95, 'bid': 62.15, 'high': 82.0, 'low': 61.3, 'ltp': 62.45, 'oi': 3507825, 'open': 70.0, 'prev_close': 83.75, 'volume': 3822750}, 'status': 'success'}\n", "NIFTY31JUL2524400PE → {'data': {'ask': 54.85, 'bid': 53.95, 'high': 71.95, 'low': 44.7, 'ltp': 54.45, 'oi': 432600, 'open': 63.25, 'prev_close': 73.25, 'volume': 2113725}, 'status': 'success'}\n", "NIFTY31JUL2524200CE → {'data': {'ask': 1637.75, 'bid': 1282.75, 'high': 1600.0, 'low': 1495.0, 'ltp': 1590.0, 'oi': 40875, 'open': 1517.4, 'prev_close': 1455.7, 'volume': 5550}, 'status': 'success'}\n", "NIFTY31JUL2524300CE → {'data': {'ask': 1576.85, 'bid': 1485.1, 'high': 1498.4, 'low': 1394.35, 'ltp': 1476.9, 'oi': 40125, 'open': 1424.75, 'prev_close': 1361.75, 'volume': 2400}, 'status': 'success'}\n", "NIFTY31JUL2524500CE → {'data': {'ask': 1319.2, 'bid': 1308.0, 'high': 1327.35, 'low': 1212.75, 'ltp': 1306.1, 'oi': 615750, 'open': 1215.0, 'prev_close': 1192.5, 'volume': 124350}, 'status': 'success'}\n", "NIFTY31JUL2524700PE → {'data': {'ask': 83.4, 'bid': 82.35, 'high': 107.65, 'low': 81.3, 'ltp': 82.85, 'oi': 798150, 'open': 95.35, 'prev_close': 109.6, 'volume': 794400}, 'status': 'success'}\n", "NIFTY31JUL2524800PE → {'data': {'ask': 96.15, 'bid': 95.1, 'high': 125.1, 'low': 94.5, 'ltp': 95.9, 'oi': 1084275, 'open': 116.95, 'prev_close': 126.15, 'volume': 1171725}, 'status': 'success'}\n", "NIFTY31JUL2524900CE → {'data': {'ask': 973.8, 'bid': 962.0, 'high': 983.0, 'low': 883.5, 'ltp': 961.65, 'oi': 297375, 'open': 939.55, 'prev_close': 862.15, 'volume': 43425}, 'status': 'success'}\n", "NIFTY31JUL2525000CE → {'data': {'ask': 884.55, 'bid': 880.0, 'high': 900.35, 'low': 796.1, 'ltp': 877.65, 'oi': 3296400, 'open': 844.0, 'prev_close': 779.0, 'volume': 742875}, 'status': 'success'}\n", "NIFTY31JUL2524600PE → {'data': {'ask': 74.0, 'bid': 71.95, 'high': 95.4, 'low': 71.55, 'ltp': 72.65, 'oi': 625125, 'open': 83.4, 'prev_close': 96.8, 'volume': 843975}, 'status': 'success'}\n", "NIFTY31JUL2524900PE → {'data': {'ask': 111.0, 'bid': 110.05, 'high': 144.0, 'low': 108.7, 'ltp': 111.25, 'oi': 622425, 'open': 144.0, 'prev_close': 144.8, 'volume': 811875}, 'status': 'success'}\n", "NIFTY31JUL2524600CE → {'data': {'ask': 1234.95, 'bid': 1220.5, 'high': 1238.1, 'low': 1129.0, 'ltp': 1221.75, 'oi': 117225, 'open': 1189.85, 'prev_close': 1113.65, 'volume': 12000}, 'status': 'success'}\n", "NIFTY31JUL2524800CE → {'data': {'ask': 1057.3, 'bid': 1046.6, 'high': 1062.0, 'low': 914.55, 'ltp': 1047.55, 'oi': 462900, 'open': 914.55, 'prev_close': 938.0, 'volume': 47925}, 'status': 'success'}\n", "NIFTY31JUL2524700CE → {'data': {'ask': 1146.35, 'bid': 1130.0, 'high': 1152.25, 'low': 1046.8, 'ltp': 1136.6, 'oi': 211275, 'open': 1100.0, 'prev_close': 1021.75, 'volume': 9675}, 'status': 'success'}\n", "NIFTY31JUL2525000PE → {'data': {'ask': 128.25, 'bid': 125.7, 'high': 164.65, 'low': 124.65, 'ltp': 127.6, 'oi': 6526875, 'open': 164.65, 'prev_close': 164.65, 'volume': 4099950}, 'status': 'success'}\n", "NIFTY31JUL2525400CE → {'data': {'ask': 586.0, 'bid': 579.05, 'high': 598.45, 'low': 510.0, 'ltp': 581.35, 'oi': 724275, 'open': 510.0, 'prev_close': 504.5, 'volume': 514275}, 'status': 'success'}\n", "NIFTY31JUL2525200CE → {'data': {'ask': 729.95, 'bid': 724.0, 'high': 742.5, 'low': 649.0, 'ltp': 722.5, 'oi': 770100, 'open': 712.0, 'prev_close': 635.8, 'volume': 326550}, 'status': 'success'}\n", "NIFTY31JUL2525100CE → {'data': {'ask': 811.5, 'bid': 799.9, 'high': 817.5, 'low': 722.75, 'ltp': 800.8, 'oi': 442050, 'open': 730.0, 'prev_close': 708.15, 'volume': 134175}, 'status': 'success'}\n", "NIFTY31JUL2525400PE → {'data': {'ask': 226.5, 'bid': 223.5, 'high': 281.25, 'low': 221.75, 'ltp': 227.45, 'oi': 906675, 'open': 259.95, 'prev_close': 285.05, 'volume': 990225}, 'status': 'success'}\n", "NIFTY31JUL2525200PE → {'data': {'ask': 170.85, 'bid': 168.1, 'high': 216.0, 'low': 166.2, 'ltp': 170.45, 'oi': 1265700, 'open': 200.05, 'prev_close': 218.9, 'volume': 1306050}, 'status': 'success'}\n", "NIFTY31JUL2525300CE → {'data': {'ask': 655.0, 'bid': 648.65, 'high': 669.6, 'low': 576.0, 'ltp': 651.0, 'oi': 687150, 'open': 625.6, 'prev_close': 567.35, 'volume': 406275}, 'status': 'success'}\n", "NIFTY31JUL2525100PE → {'data': {'ask': 148.0, 'bid': 146.05, 'high': 188.4, 'low': 144.9, 'ltp': 148.2, 'oi': 768525, 'open': 174.0, 'prev_close': 191.15, 'volume': 937500}, 'status': 'success'}\n", "NIFTY31JUL2525500CE → {'data': {'ask': 518.95, 'bid': 513.6, 'high': 531.1, 'low': 452.9, 'ltp': 513.6, 'oi': 2594625, 'open': 465.0, 'prev_close': 440.6, 'volume': 2631225}, 'status': 'success'}\n", "NIFTY31JUL2525500PE → {'data': {'ask': 256.8, 'bid': 255.05, 'high': 317.0, 'low': 253.25, 'ltp': 258.75, 'oi': 2561025, 'open': 300.9, 'prev_close': 320.8, 'volume': 4119750}, 'status': 'success'}\n", "NIFTY31JUL2525300PE → {'data': {'ask': 195.15, 'bid': 193.95, 'high': 247.05, 'low': 192.6, 'ltp': 196.7, 'oi': 963525, 'open': 230.0, 'prev_close': 250.4, 'volume': 1212450}, 'status': 'success'}\n", "NIFTY31JUL2525600PE → {'data': {'ask': 295.0, 'bid': 293.1, 'high': 360.0, 'low': 291.05, 'ltp': 298.15, 'oi': 594600, 'open': 350.0, 'prev_close': 363.9, 'volume': 1747500}, 'status': 'success'}\n", "NIFTY31JUL2526000CE → {'data': {'ask': 249.95, 'bid': 247.15, 'high': 260.7, 'low': 211.65, 'ltp': 247.6, 'oi': 3868350, 'open': 225.0, 'prev_close': 202.1, 'volume': 5025450}, 'status': 'success'}\n", "NIFTY31JUL2526000PE → {'data': {'ask': 485.9, 'bid': 481.15, 'high': 570.0, 'low': 479.65, 'ltp': 488.05, 'oi': 1225050, 'open': 550.0, 'prev_close': 578.05, 'volume': 943200}, 'status': 'success'}\n", "NIFTY31JUL2525600CE → {'data': {'ask': 458.0, 'bid': 453.45, 'high': 471.0, 'low': 387.25, 'ltp': 453.35, 'oi': 671775, 'open': 387.25, 'prev_close': 387.25, 'volume': 1803225}, 'status': 'success'}\n", "NIFTY31JUL2525700PE → {'data': {'ask': 336.0, 'bid': 333.3, 'high': 405.3, 'low': 331.0, 'ltp': 338.45, 'oi': 584775, 'open': 390.0, 'prev_close': 409.4, 'volume': 1312350}, 'status': 'success'}\n", "NIFTY31JUL2525700CE → {'data': {'ask': 400.0, 'bid': 394.0, 'high': 409.5, 'low': 342.5, 'ltp': 394.65, 'oi': 801000, 'open': 376.0, 'prev_close': 329.0, 'volume': 1562025}, 'status': 'success'}\n", "NIFTY31JUL2525900PE → {'data': {'ask': 432.0, 'bid': 428.2, 'high': 516.5, 'low': 424.2, 'ltp': 432.85, 'oi': 60675, 'open': 516.5, 'prev_close': 516.75, 'volume': 92175}, 'status': 'success'}\n", "NIFTY31JUL2525900CE → {'data': {'ask': 293.0, 'bid': 291.0, 'high': 304.6, 'low': 252.15, 'ltp': 291.05, 'oi': 355200, 'open': 284.85, 'prev_close': 240.6, 'volume': 575100}, 'status': 'success'}\n", "NIFTY31JUL2525800PE → {'data': {'ask': 382.75, 'bid': 377.7, 'high': 452.5, 'low': 375.0, 'ltp': 382.55, 'oi': 559725, 'open': 402.0, 'prev_close': 461.35, 'volume': 1125075}, 'status': 'success'}\n", "NIFTY31JUL2525800CE → {'data': {'ask': 345.0, 'bid': 340.0, 'high': 354.8, 'low': 296.4, 'ltp': 340.8, 'oi': 836850, 'open': 310.0, 'prev_close': 284.1, 'volume': 1346400}, 'status': 'success'}\n", "NIFTY31JUL2526500CE → {'data': {'ask': 94.8, 'bid': 94.0, 'high': 101.6, 'low': 79.5, 'ltp': 93.5, 'oi': 3472875, 'open': 88.0, 'prev_close': 75.6, 'volume': 3895800}, 'status': 'success'}\n", "NIFTY31JUL2526200PE → {'data': {'ask': 617.75, 'bid': 600.05, 'high': 700.3, 'low': 604.0, 'ltp': 613.5, 'oi': 27150, 'open': 645.0, 'prev_close': 712.6, 'volume': 30075}, 'status': 'success'}\n", "NIFTY31JUL2526300CE → {'data': {'ask': 143.0, 'bid': 141.1, 'high': 151.1, 'low': 120.1, 'ltp': 141.0, 'oi': 590925, 'open': 135.35, 'prev_close': 114.7, 'volume': 1154475}, 'status': 'success'}\n", "NIFTY31JUL2526200CE → {'data': {'ask': 174.5, 'bid': 171.65, 'high': 183.55, 'low': 146.05, 'ltp': 171.5, 'oi': 983775, 'open': 164.95, 'prev_close': 137.55, 'volume': 1425000}, 'status': 'success'}\n", "NIFTY31JUL2526300PE → {'data': {'ask': 686.45, 'bid': 667.35, 'high': 768.4, 'low': 670.05, 'ltp': 677.5, 'oi': 14700, 'open': 716.0, 'prev_close': 783.1, 'volume': 16050}, 'status': 'success'}\n", "NIFTY31JUL2526100CE → {'data': {'ask': 208.0, 'bid': 205.8, 'high': 218.9, 'low': 168.0, 'ltp': 206.3, 'oi': 422775, 'open': 168.0, 'prev_close': 168.0, 'volume': 653400}, 'status': 'success'}\n", "NIFTY31JUL2526400PE → {'data': {'ask': 757.4, 'bid': 734.3, 'high': 860.0, 'low': 746.85, 'ltp': 755.1, 'oi': 7050, 'open': 860.0, 'prev_close': 862.2, 'volume': 7875}, 'status': 'success'}\n", "NIFTY31JUL2526400CE → {'data': {'ask': 116.9, 'bid': 116.0, 'high': 125.0, 'low': 90.0, 'ltp': 116.3, 'oi': 527400, 'open': 110.0, 'prev_close': 92.65, 'volume': 778650}, 'status': 'success'}\n", "NIFTY31JUL2526100PE → {'data': {'ask': 553.65, 'bid': 534.4, 'high': 629.05, 'low': 538.0, 'ltp': 547.8, 'oi': 26625, 'open': 579.5, 'prev_close': 644.6, 'volume': 32250}, 'status': 'success'}\n", "NIFTY31JUL2526500PE → {'data': {'ask': 828.9, 'bid': 822.6, 'high': 928.0, 'low': 816.0, 'ltp': 827.2, 'oi': 1096200, 'open': 876.05, 'prev_close': 943.1, 'volume': 323850}, 'status': 'success'}\n", "NIFTY31JUL2527000CE → {'data': {'ask': 29.95, 'bid': 29.75, 'high': 41.35, 'low': 25.0, 'ltp': 29.3, 'oi': 1218000, 'open': 28.0, 'prev_close': 24.9, 'volume': 2738100}, 'status': 'success'}\n", "NIFTY31JUL2526900PE → {'data': {'ask': 1174.9, 'bid': 1149.0, 'high': 1270.0, 'low': 1158.5, 'ltp': 1161.95, 'oi': 162375, 'open': 1200.0, 'prev_close': 1289.6, 'volume': 32775}, 'status': 'success'}\n", "NIFTY31JUL2526800PE → {'data': {'ask': 1085.05, 'bid': 1062.8, 'high': 1175.0, 'low': 1062.25, 'ltp': 1078.6, 'oi': 10650, 'open': 1103.75, 'prev_close': 1206.4, 'volume': 4050}, 'status': 'success'}\n", "NIFTY31JUL2526900CE → {'data': {'ask': 38.5, 'bid': 37.65, 'high': 54.6, 'low': 31.9, 'ltp': 37.5, 'oi': 1013700, 'open': 32.55, 'prev_close': 31.45, 'volume': 3382650}, 'status': 'success'}\n", "NIFTY31JUL2526800CE → {'data': {'ask': 47.5, 'bid': 47.0, 'high': 59.15, 'low': 40.3, 'ltp': 46.6, 'oi': 1206450, 'open': 42.5, 'prev_close': 38.5, 'volume': 1316925}, 'status': 'success'}\n", "NIFTY31JUL2526600CE → {'data': {'ask': 76.65, 'bid': 72.9, 'high': 82.0, 'low': 63.9, 'ltp': 75.15, 'oi': 499425, 'open': 68.4, 'prev_close': 60.4, 'volume': 615975}, 'status': 'success'}\n", "NIFTY31JUL2527000PE → {'data': {'ask': 1255.0, 'bid': 1248.4, 'high': 1364.2, 'low': 1240.0, 'ltp': 1258.4, 'oi': 390375, 'open': 1312.5, 'prev_close': 1384.7, 'volume': 211950}, 'status': 'success'}\n", "NIFTY31JUL2526700PE → {'data': {'ask': 1001.8, 'bid': 854.7, 'high': 1059.9, 'low': 982.0, 'ltp': 994.5, 'oi': 4950, 'open': 1059.9, 'prev_close': 1235.45, 'volume': 3675}, 'status': 'success'}\n", "NIFTY31JUL2526600PE → {'data': {'ask': 1099.4, 'bid': 828.15, 'high': 1000.0, 'low': 900.0, 'ltp': 907.0, 'oi': 10350, 'open': 939.0, 'prev_close': 1020.45, 'volume': 2400}, 'status': 'success'}\n", "NIFTY31JUL2526700CE → {'data': {'ask': 62.0, 'bid': 59.3, 'high': 66.55, 'low': 50.95, 'ltp': 59.6, 'oi': 692400, 'open': 54.95, 'prev_close': 47.85, 'volume': 912750}, 'status': 'success'}\n", "NIFTY31JUL2527100PE → {'data': {'ask': 1589.35, 'bid': 1224.25, 'high': 0.0, 'low': 0.0, 'ltp': 2533.55, 'oi': 0, 'open': 0.0, 'prev_close': 2533.55, 'volume': 0}, 'status': 'success'}\n", "NIFTY31JUL2527500CE → {'status': 'error', 'message': 'HTTP 500: {\"message\":\"Error fetching quotes: Error from Angel API: Invalid exchange tokens.\",\"status\":\"error\"}\\n', 'code': 500, 'error_type': 'http_error'}\n", "NIFTY31JUL2527500PE → {'status': 'error', 'message': 'HTTP 500: {\"message\":\"Error fetching quotes: Error from Angel API: Invalid exchange tokens.\",\"status\":\"error\"}\\n', 'code': 500, 'error_type': 'http_error'}\n", "NIFTY31JUL2527200PE → {'data': {'ask': 1673.35, 'bid': 1325.1, 'high': 0.0, 'low': 0.0, 'ltp': 2618.9, 'oi': 0, 'open': 0.0, 'prev_close': 2618.9, 'volume': 0}, 'status': 'success'}\n", "NIFTY31JUL2527400PE → {'data': {'ask': 97.0, 'bid': 96.5, 'high': 132.9, 'low': 80.7, 'ltp': 96.7, 'oi': 73500, 'open': 119.1, 'prev_close': 125.75, 'volume': 1944900}, 'status': 'success'}\n", "NIFTY31JUL2527300PE → {'data': {'ask': 1552.95, 'bid': 1411.15, 'high': 1640.0, 'low': 1520.0, 'ltp': 1530.0, 'oi': 5100, 'open': 1610.0, 'prev_close': 2705.05, 'volume': 5100}, 'status': 'success'}\n", "NIFTY31JUL2527400CE → {'data': {'ask': 0.05, 'bid': 0.0, 'high': 10.45, 'low': 0.05, 'ltp': 0.05, 'oi': 5463750, 'open': 5.1, 'prev_close': 13.25, 'volume': 162237450}, 'status': 'success'}\n", "NIFTY31JUL2527300CE → {'data': {'ask': 15.65, 'bid': 14.95, 'high': 16.05, 'low': 12.85, 'ltp': 14.9, 'oi': 216150, 'open': 13.6, 'prev_close': 142.8, 'volume': 510975}, 'status': 'success'}\n", "NIFTY31JUL2527100CE → {'data': {'ask': 56.4, 'bid': 23.0, 'high': 0.0, 'low': 0.0, 'ltp': 167.7, 'oi': 0, 'open': 0.0, 'prev_close': 167.7, 'volume': 0}, 'status': 'success'}\n", "NIFTY31JUL2527200CE → {'data': {'ask': 19.9, 'bid': 19.0, 'high': 39.4, 'low': 15.95, 'ltp': 19.0, 'oi': 122925, 'open': 39.4, 'prev_close': 154.8, 'volume': 274425}, 'status': 'success'}\n", "NIFTY31JUL2527500CE → {'status': 'error', 'message': 'HTTP 429: {\"message\": \"10 per 1 second\"}\\n', 'code': 429, 'error_type': 'http_error'}\n", "NIFTY31JUL2527500PE → {'status': 'error', 'message': 'HTTP 429: {\"message\": \"10 per 1 second\"}\\n', 'code': 429, 'error_type': 'http_error'}\n", "NIFTY31JUL2527600PE → {'status': 'error', 'message': 'HTTP 500: {\"message\":\"Error fetching quotes: Error from Angel API: Invalid exchange tokens.\",\"status\":\"error\"}\\n', 'code': 500, 'error_type': 'http_error'}\n", "NIFTY31JUL2527600CE → {'status': 'error', 'message': 'HTTP 500: {\"message\":\"Error fetching quotes: Error from Angel API: Invalid exchange tokens.\",\"status\":\"error\"}\\n', 'code': 500, 'error_type': 'http_error'}\n", "NIFTY31JUL2527600PE → {'status': 'error', 'message': 'HTTP 500: {\"message\":\"Error fetching quotes: Error from Angel API: Invalid exchange tokens.\",\"status\":\"error\"}\\n', 'code': 500, 'error_type': 'http_error'}\n", "NIFTY31JUL2527600CE → {'status': 'error', 'message': 'HTTP 500: {\"message\":\"Error fetching quotes: Error from Angel API: Invalid exchange tokens.\",\"status\":\"error\"}\\n', 'code': 500, 'error_type': 'http_error'}\n", "   strike type      oi      ltp\n", "0   23600   CE   41325  2149.40\n", "1   23600   PE  205125    20.85\n", "2   23700   CE    8175  2051.00\n", "3   23700   PE  342450    22.80\n", "4   23800   CE   31950  1943.30\n", "Underlying Quote : {'data': {'ask': 0.0, 'bid': 0.0, 'high': 25654.2, 'low': 25523.55, 'ltp': 25637.8, 'oi': 0, 'open': 25576.65, 'prev_close': 25549.0, 'volume': 0}, 'status': 'success'}\n"]}, {"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"customdata": {"bdata": "AAAAAKAt5EDNzMzMzMqgQAAAAAAA779AAAAAAAAGoEAAAAAAgDPfQDMzMzMzXZ5AAAAAAMBe3UAAAAAAAI6dQAAAAAAo+CtBZmZmZmagm0AAAAAAwGjTQJqZmZmZwplAAAAAAGD140AAAAAAANiYQAAAAACgl+NAmpmZmZkTl0AAAAAAwK7oQAAAAAAA3JVAAAAAAIzKIkFmZmZmZmiUQAAAAACQnvxAAAAAAAAXk0AAAAAAWMoJQWZmZmZmwpFAAAAAANBAHEEzMzMzM16QQAAAAAB8JhJBMzMzMzMNjkAAAAAASCZJQTMzMzMzbYtAAAAAAAj7GkFmZmZmZgaJQAAAAABogCdBAAAAAACUhkAAAAAAXPgkQQAAAAAAWIRAAAAAAGYaJkHNzMzMzCqCQAAAAICgy0NBzczMzMwMgEAAAAAAPoAkQZqZmZmZVXxAAAAAANBxKEFmZmZmZqp4QAAAAADkiSlBzczMzMxMdUAAAAAAAK4VQc3MzMzMMHJAAAAAAF+DTUEzMzMzM/NuQAAAAADczRlBmpmZmZnJaUAAAAAAvgUuQQAAAAAAcGVAAAAAAJoIIkEAAAAAAKBhQAAAAABQGCBBMzMzMzMTXUAAAACA9X5KQQAAAAAAYFdAAAAAAIR7HkGamZmZmclSQAAAAABgISVBzczMzMzMTUAAAAAAsmgyQc3MzMzMTEdAAAAAAIjvLkEAAAAAAMBCQAAAAADQlTJBzczMzMxMPUAAAAAAAAAAAGZmZmZm9mRAAAAAANAC/kAAAAAAAAAzQAAAAACwYgpBzczMzMzMLUAAAACAsddUQZqZmZmZmak/", "dtype": "f8", "shape": "39, 2"}, "hovertemplate": "<b>%{x} CE</b><br>OI %{customdata[0]:,}<br>LTP ₹%{customdata[1]:.2f}<extra></extra>", "marker": {"color": "seagreen"}, "name": "Call OI", "type": "bar", "x": {"bdata": "MFyUXPhcXF3AXSReiF7sXlBftF8YYHxg4GBEYahhDGJwYtRiOGOcYwBkZGTIZCxlkGX0ZVhmvGYgZ4Rn6GdMaLBoFGl4adxpQGqkaghr", "dtype": "i2"}, "y": {"bdata": "AAAAAKAt5EAAAAAAAO+/QAAAAACAM99AAAAAAMBe3UAAAAAAKPgrQQAAAADAaNNAAAAAAGD140AAAAAAoJfjQAAAAADAruhAAAAAAIzKIkEAAAAAkJ78QAAAAABYyglBAAAAANBAHEEAAAAAfCYSQQAAAABIJklBAAAAAAj7GkEAAAAAaIAnQQAAAABc+CRBAAAAAGYaJkEAAACAoMtDQQAAAAA+gCRBAAAAANBxKEEAAAAA5IkpQQAAAAAArhVBAAAAAF+DTUEAAAAA3M0ZQQAAAAC+BS5BAAAAAJoIIkEAAAAAUBggQQAAAID1fkpBAAAAAIR7HkEAAAAAYCElQQAAAACyaDJBAAAAAIjvLkEAAAAA0JUyQQAAAAAAAAAAAAAAANAC/kAAAAAAsGIKQQAAAICx11RB", "dtype": "f8"}}, {"customdata": {"bdata": "AAAAACgKCUGamZmZmdk0QAAAAADI5hRBzczMzMzMNkAAAAAAWBolQQAAAAAAADlAAAAAAMSzHkHNzMzMzMw8QAAAAIBJDUpBmpmZmZmZQEAAAAAAlC4QQQAAAAAAYEJAAAAAAAQIIkEzMzMzM/NEQAAAAABsqRdBmpmZmZm5R0AAAAAAYGcaQZqZmZmZOUtAAAAAgDjDSkGamZmZmTlPQAAAAADKEyNBmpmZmZkpUkAAAAAAjFsoQWZmZmZmtlRAAAAAAHOLMEGamZmZmflXQAAAAACy/iJBAAAAAADQW0AAAADA5uVYQWZmZmZm5l9AAAAAABp0J0FmZmZmZoZiQAAAAAAkUDNBZmZmZmZOZUAAAAAAimctQWZmZmZmlmhAAAAAAGarK0FmZmZmZm5sQAAAAIAAikNBAAAAAAAscEAAAAAAUCUiQWZmZmZmonJAAAAAAI7YIUEzMzMzMyd1QAAAAADaFCFBzczMzMzod0AAAAAAYKDtQJqZmZmZDXtAAAAAAFqxMkHNzMzMzIB+QAAAAABAANpAZmZmZmYegUAAAAAAgIPaQAAAAAAALINAAAAAAAC2zEAAAAAAACyFQAAAAAAAirtAzczMzMyYh0AAAAAACLowQZqZmZmZ2YlAAAAAAAA3xEAAAAAAAFiMQAAAAAAAVrNAAAAAAAAUj0AAAAAAAM3EQGZmZmZm2pBAAAAAADjSA0HNzMzMzCeSQAAAAACc0xdBmpmZmZmpk0AAAAAAAAAAAJqZmZkZy6NAAAAAAAAAAADNzMzMzHWkQAAAAAAA7LNAAAAAAADol0AAAAAAwPHxQM3MzMzMLFhA", "dtype": "f8", "shape": "39, 2"}, "hovertemplate": "<b>%{x} PE</b><br>OI %{customdata[0]:,}<br>LTP ₹%{customdata[1]:.2f}<extra></extra>", "marker": {"color": "crimson"}, "name": "Put OI", "type": "bar", "x": {"bdata": "MFyUXPhcXF3AXSReiF7sXlBftF8YYHxg4GBEYahhDGJwYtRiOGOcYwBkZGTIZCxlkGX0ZVhmvGYgZ4Rn6GdMaLBoFGl4adxpQGqkaghr", "dtype": "i2"}, "y": {"bdata": "AAAAACgKCUEAAAAAyOYUQQAAAABYGiVBAAAAAMSzHkEAAACASQ1KQQAAAACULhBBAAAAAAQIIkEAAAAAbKkXQQAAAABgZxpBAAAAgDjDSkEAAAAAyhMjQQAAAACMWyhBAAAAAHOLMEEAAAAAsv4iQQAAAMDm5VhBAAAAABp0J0EAAAAAJFAzQQAAAACKZy1BAAAAAGarK0EAAACAAIpDQQAAAABQJSJBAAAAAI7YIUEAAAAA2hQhQQAAAABgoO1AAAAAAFqxMkEAAAAAQADaQAAAAACAg9pAAAAAAAC2zEAAAAAAAIq7QAAAAAAIujBBAAAAAAA3xEAAAAAAAFazQAAAAAAAzcRAAAAAADjSA0EAAAAAnNMXQQAAAAAAAAAAAAAAAAAAAAAAAAAAAOyzQAAAAADA8fFA", "dtype": "f8"}}], "layout": {"annotations": [{"showarrow": false, "text": "ATM 25600", "x": 20, "xanchor": "center", "xref": "x", "y": 1, "yanchor": "bottom", "yref": "y domain"}], "bargap": 0.05, "height": 500, "legend": {"orientation": "h", "x": 1, "xanchor": "right", "y": 1.02, "yanchor": "bottom"}, "shapes": [{"line": {"color": "gray", "dash": "dash"}, "type": "line", "x0": 20, "x1": 20, "xref": "x", "y0": 0, "y1": 1, "yref": "y domain"}], "template": {"data": {"bar": [{"error_x": {"color": "#f2f5fa"}, "error_y": {"color": "#f2f5fa"}, "marker": {"line": {"color": "rgb(17,17,17)", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "rgb(17,17,17)", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#A2B1C6", "gridcolor": "#506784", "linecolor": "#506784", "minorgridcolor": "#506784", "startlinecolor": "#A2B1C6"}, "baxis": {"endlinecolor": "#A2B1C6", "gridcolor": "#506784", "linecolor": "#506784", "minorgridcolor": "#506784", "startlinecolor": "#A2B1C6"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"line": {"color": "#283442"}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"line": {"color": "#283442"}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#506784"}, "line": {"color": "rgb(17,17,17)"}}, "header": {"fill": {"color": "#2a3f5f"}, "line": {"color": "rgb(17,17,17)"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#f2f5fa", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#f2f5fa"}, "geo": {"bgcolor": "rgb(17,17,17)", "lakecolor": "rgb(17,17,17)", "landcolor": "rgb(17,17,17)", "showlakes": true, "showland": true, "subunitcolor": "#506784"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "dark"}, "paper_bgcolor": "rgb(17,17,17)", "plot_bgcolor": "rgb(17,17,17)", "polar": {"angularaxis": {"gridcolor": "#506784", "linecolor": "#506784", "ticks": ""}, "bgcolor": "rgb(17,17,17)", "radialaxis": {"gridcolor": "#506784", "linecolor": "#506784", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "rgb(17,17,17)", "gridcolor": "#506784", "gridwidth": 2, "linecolor": "#506784", "showbackground": true, "ticks": "", "zerolinecolor": "#C8D4E3"}, "yaxis": {"backgroundcolor": "rgb(17,17,17)", "gridcolor": "#506784", "gridwidth": 2, "linecolor": "#506784", "showbackground": true, "ticks": "", "zerolinecolor": "#C8D4E3"}, "zaxis": {"backgroundcolor": "rgb(17,17,17)", "gridcolor": "#506784", "gridwidth": 2, "linecolor": "#506784", "showbackground": true, "ticks": "", "zerolinecolor": "#C8D4E3"}}, "shapedefaults": {"line": {"color": "#f2f5fa"}}, "sliderdefaults": {"bgcolor": "#C8D4E3", "bordercolor": "rgb(17,17,17)", "borderwidth": 1, "tickwidth": 0}, "ternary": {"aaxis": {"gridcolor": "#506784", "linecolor": "#506784", "ticks": ""}, "baxis": {"gridcolor": "#506784", "linecolor": "#506784", "ticks": ""}, "bgcolor": "rgb(17,17,17)", "caxis": {"gridcolor": "#506784", "linecolor": "#506784", "ticks": ""}}, "title": {"x": 0.05}, "updatemenudefaults": {"bgcolor": "#506784", "borderwidth": 0}, "xaxis": {"automargin": true, "gridcolor": "#283442", "linecolor": "#506784", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "#283442", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "#283442", "linecolor": "#506784", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "#283442", "zerolinewidth": 2}}}, "title": {"text": "NIFTY 31 Jul 2025 – OI Profile"}, "width": 1250, "xaxis": {"title": {"text": "Strike"}, "type": "category"}, "yaxis": {"title": {"text": "Open Interest"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["\"\"\"\n", "NIFTY 31 JUL 2025 – OI profile (10-req/s batches)\n", "Author  : OpenAlgo GPT\n", "Updated : 2025-06-28\n", "\"\"\"\n", "\n", "print(\"🔁 OpenAlgo Python Bot is running.\")                     # rule 13\n", "\n", "import os, sys, re, time, asyncio, pandas as pd, plotly.graph_objects as go\n", "from datetime import datetime\n", "from openalgo import api\n", "\n", "# ───────────── CO<PERSON><PERSON> (edit to suit) ─────────────────────────────────────\n", "API_KEY  = os.getenv(\"OPENALGO_API_KEY\",  \"openalgo-api-key\")\n", "API_HOST = os.getenv(\"OPENALGO_API_HOST\", \"http://127.0.0.1:5000\")\n", "\n", "EXPIRY        = \"31JUL25\"      # ✅ option expiry\n", "RADIUS        = 20             # ± strikes\n", "STEP          = 100            # ✅ 100-pt strikes (was 50)\n", "BATCH_SIZE    = 10             # ✅ broker cap per second\n", "BATCH_PAUSE   = 2           # seconds to wait between batches\n", "MAX_RETRIES   = 1              # one retry on 429 / timeout\n", "BACKOFF_SEC   = 1.2\n", "\n", "client = api(api_key=API_KEY, host=API_HOST)\n", "if sys.platform.startswith(\"win\"):\n", "    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())\n", "\n", "# ───────────── Helpers ───────────────────────────────────────────────────\n", "def get_atm_strike(step: int = STEP) -> int:\n", "    q = client.quotes(symbol=\"NIFTY\", exchange=\"NSE_INDEX\")\n", "    print(\"Underlying Quote :\", q)                               # rule 14\n", "    return int(round(q[\"data\"][\"ltp\"] / step) * step)\n", "\n", "_sym_rx = re.compile(r\"^[A-Z]+(\\d{2}[A-Z]{3}\\d{2})(\\d+)(CE|PE)$\")\n", "def parse_symbol(sym: str):\n", "    m = _sym_rx.match(sym)\n", "    return (int(m.group(2)), m.group(3)) if m else None\n", "\n", "def fetch_sync(sym: str) -> dict | None:\n", "    \"\"\"Blocking quote call with a retry for 429 / timeout.\"\"\"\n", "    for attempt in range(MAX_RETRIES + 1):\n", "        q = client.quotes(symbol=sym, exchange=\"NFO\")\n", "        print(sym, \"→\", q)                                       # rule 14\n", "        if q.get(\"status\") == \"success\":\n", "            strike, opt = parse_symbol(sym)\n", "            if strike is None:\n", "                print(\"⚠️  Bad symbol\", sym)\n", "                return None\n", "            return dict(strike=strike, type=opt,\n", "                        oi=q[\"data\"][\"oi\"], ltp=q[\"data\"][\"ltp\"])\n", "        if (q.get(\"code\") == 429 or q.get(\"error_type\") == \"timeout_error\") and attempt < MAX_RETRIES:\n", "            time.sleep(BACKOFF_SEC)\n", "    return None\n", "\n", "# ───────────── Batch-paced gather (≈5 req/s) ─────────────────────────────\n", "async def gather_df() -> pd.DataFrame:\n", "    atm = get_atm_strike()\n", "    strikes = [atm + i*STEP for i in range(-RADIUS, RADIUS + 1)]\n", "    symbols = [f\"NIFTY{EXPIRY}{k}{s}\" for k in strikes for s in (\"CE\", \"PE\")]\n", "\n", "    rows: list[dict] = []\n", "    for i in range(0, len(symbols), BATCH_SIZE):\n", "        batch = symbols[i:i+BATCH_SIZE]\n", "        res   = await asyncio.gather(*[asyncio.to_thread(fetch_sync, s) for s in batch])\n", "        rows.extend(r for r in res if r)\n", "        if i + BATCH_SIZE < len(symbols):\n", "            await asyncio.sleep(BATCH_PAUSE)          # pace → 5 req/s\n", "\n", "    if not rows:\n", "        raise RuntimeError(\"All quotes failed – check API / symbols.\")\n", "    return pd.DataFrame(rows)\n", "\n", "# ───────────── Plot (Call = green, Put = red, both positive) ─────────────\n", "def plot_oi(df: pd.DataFrame):\n", "    piv = (df.pivot(index=\"strike\", columns=\"type\", values=[\"oi\", \"ltp\"])\n", "             .sort_index())\n", "    piv.columns = [\"CE_OI\", \"PE_OI\", \"CE_LTP\", \"PE_LTP\"]\n", "    piv = piv.reset_index()\n", "\n", "    fig = go.Figure()\n", "    fig.add_bar(\n", "        x=piv[\"strike\"], y=piv[\"CE_OI\"],          # no minus sign\n", "        name=\"Call OI\", marker_color=\"seagreen\",  # ✅ green\n", "        customdata=piv[[\"CE_OI\", \"CE_LTP\"]],\n", "        hovertemplate=\"<b>%{x} CE</b><br>OI %{customdata[0]:,}\"\n", "                      \"<br>LTP ₹%{customdata[1]:.2f}<extra></extra>\"\n", "    )\n", "    fig.add_bar(\n", "        x=piv[\"strike\"], y=piv[\"PE_OI\"],\n", "        name=\"Put OI\", marker_color=\"crimson\",    # ✅ red\n", "        customdata=piv[[\"PE_OI\", \"PE_LTP\"]],\n", "        hovertemplate=\"<b>%{x} PE</b><br>OI %{customdata[0]:,}\"\n", "                      \"<br>LTP ₹%{customdata[1]:.2f}<extra></extra>\"\n", "    )\n", "    atm = get_atm_strike()\n", "    fig.add_vline(\n", "        x=piv.index[piv[\"strike\"] == atm][0],\n", "        line_dash=\"dash\", line_color=\"gray\",\n", "        annotation_text=f\"ATM {atm}\", annotation_position=\"top\"\n", "    )\n", "    fig.update_layout(\n", "        title=f\"NIFTY {datetime.strptime(EXPIRY,'%d%b%y').strftime('%d %b %Y')} – OI Profile\",\n", "        xaxis=dict(title=\"Strike\", type=\"category\"),\n", "        yaxis_title=\"Open Interest\",\n", "        bargap=0.05, template=\"plotly_dark\",\n", "        height=500, width=1250,\n", "        legend=dict(orientation=\"h\", yanchor=\"bottom\", y=1.02,\n", "                    xanchor=\"right\", x=1)\n", "    )\n", "    fig.show()\n", "\n", "# ───────────── Runner (script / notebook) ────────────────────────────────\n", "async def _main():\n", "    df = await gather_df()\n", "    print(df.head())\n", "    plot_oi(df)\n", "\n", "def _in_nb() -> bool:\n", "    try:\n", "        import IPython\n", "        return IPython.get_ipython() is not None\n", "    except ImportError:\n", "        return False\n", "\n", "if _in_nb():\n", "    await _main()                   # <PERSON><PERSON>ter\n", "else:\n", "    asyncio.run(_main())            # script\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}