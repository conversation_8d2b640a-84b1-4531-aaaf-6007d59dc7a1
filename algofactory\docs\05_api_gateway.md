# AlgoFactory - API Gateway Design

## Overview

The API Gateway serves as the central entry point for all client requests, handling authentication, routing, rate limiting, and request transformation. It abstracts the complexity of multiple OpenAlgo instances and provides a unified API interface.

## Gateway Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Apps   │    │   Web Frontend  │    │  Mobile Apps    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │     Load Balancer         │
                    │     (AWS ALB)             │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │   API Gateway             │
                    │   (FastAPI)               │
                    │                           │
                    │ ┌─────────────────────┐   │
                    │ │ Authentication      │   │
                    │ │ Rate Limiting       │   │
                    │ │ Request Routing     │   │
                    │ │ Response Transform  │   │
                    │ └─────────────────────┘   │
                    └─────────────┬─────────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                       │                        │
┌───────┴────────┐    ┌─────────┴─────────┐    ┌─────────┴─────────┐
│ OpenAlgo       │    │ OpenAlgo          │    │ OpenAlgo          │
│ Instance #1    │    │ Instance #2       │    │ Instance #3       │
│ (User A-Zerodha)│   │ (User A-Angel)    │    │ (User B-Upstox)   │
└────────────────┘    └───────────────────┘    └───────────────────┘
```

## FastAPI Gateway Implementation

### Main Gateway Application

```python
# src/gateway/main.py
from fastapi import FastAPI, Request, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import uvicorn
import asyncio
from contextlib import asynccontextmanager

from .middleware import (
    RateLimitMiddleware,
    LoggingMiddleware,
    AuthenticationMiddleware
)
from .routers import (
    auth_router,
    instances_router,
    trading_router,
    analytics_router
)
from .services import (
    AuthService,
    InstanceService,
    ProxyService
)
from .config import settings

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await startup_event()
    yield
    # Shutdown
    await shutdown_event()

app = FastAPI(
    title="AlgoFactory API Gateway",
    description="Multi-tenant trading platform API",
    version="1.0.0",
    lifespan=lifespan
)

# Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)

app.add_middleware(RateLimitMiddleware)
app.add_middleware(LoggingMiddleware)
app.add_middleware(AuthenticationMiddleware)

# Routers
app.include_router(auth_router, prefix="/api/v1/auth", tags=["authentication"])
app.include_router(instances_router, prefix="/api/v1/instances", tags=["instances"])
app.include_router(trading_router, prefix="/api/v1/trading", tags=["trading"])
app.include_router(analytics_router, prefix="/api/v1/analytics", tags=["analytics"])

# Health check
@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.utcnow()}

async def startup_event():
    """Initialize services on startup"""
    app.state.auth_service = AuthService()
    app.state.instance_service = InstanceService()
    app.state.proxy_service = ProxyService()
    
    # Initialize connection pools
    await app.state.proxy_service.initialize_pools()

async def shutdown_event():
    """Cleanup on shutdown"""
    await app.state.proxy_service.cleanup_pools()

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG
    )
```

### Authentication Middleware

```python
# src/gateway/middleware/auth.py
from fastapi import Request, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import jwt
from typing import Optional
import time

class AuthenticationMiddleware:
    def __init__(self):
        self.security = HTTPBearer(auto_error=False)
        self.public_paths = {
            "/health",
            "/api/v1/auth/login",
            "/api/v1/auth/register",
            "/docs",
            "/openapi.json"
        }
    
    async def __call__(self, request: Request, call_next):
        # Skip authentication for public paths
        if request.url.path in self.public_paths:
            return await call_next(request)
        
        # Extract token
        authorization = request.headers.get("Authorization")
        if not authorization:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authorization header required"
            )
        
        try:
            scheme, token = authorization.split()
            if scheme.lower() != "bearer":
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid authentication scheme"
                )
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authorization header format"
            )
        
        # Validate token
        try:
            payload = jwt.decode(
                token,
                settings.JWT_SECRET_KEY,
                algorithms=[settings.JWT_ALGORITHM]
            )
            
            # Check expiration
            if payload.get("exp", 0) < time.time():
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token expired"
                )
            
            # Add user info to request state
            request.state.user_id = payload.get("user_id")
            request.state.user_email = payload.get("email")
            request.state.permissions = payload.get("permissions", [])
            
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
        
        return await call_next(request)
```

### Rate Limiting Middleware

```python
# src/gateway/middleware/rate_limit.py
from fastapi import Request, HTTPException, status
import asyncio
import time
from collections import defaultdict
from typing import Dict, Tuple
import redis.asyncio as redis

class RateLimitMiddleware:
    def __init__(self):
        self.redis_client = redis.from_url(settings.REDIS_URL)
        self.rate_limits = {
            "default": (100, 60),  # 100 requests per minute
            "trading": (50, 60),   # 50 trading requests per minute
            "analytics": (200, 60), # 200 analytics requests per minute
        }
    
    async def __call__(self, request: Request, call_next):
        # Skip rate limiting for health checks
        if request.url.path == "/health":
            return await call_next(request)
        
        # Determine rate limit category
        category = self._get_rate_limit_category(request.url.path)
        limit, window = self.rate_limits.get(category, self.rate_limits["default"])
        
        # Get client identifier
        client_id = self._get_client_id(request)
        
        # Check rate limit
        if await self._is_rate_limited(client_id, category, limit, window):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded",
                headers={"Retry-After": str(window)}
            )
        
        return await call_next(request)
    
    def _get_rate_limit_category(self, path: str) -> str:
        if "/trading/" in path:
            return "trading"
        elif "/analytics/" in path:
            return "analytics"
        return "default"
    
    def _get_client_id(self, request: Request) -> str:
        # Use user_id if authenticated, otherwise IP address
        if hasattr(request.state, 'user_id'):
            return f"user:{request.state.user_id}"
        return f"ip:{request.client.host}"
    
    async def _is_rate_limited(self, client_id: str, category: str, 
                             limit: int, window: int) -> bool:
        key = f"rate_limit:{client_id}:{category}"
        current_time = int(time.time())
        window_start = current_time - window
        
        # Use Redis sliding window
        pipe = self.redis_client.pipeline()
        pipe.zremrangebyscore(key, 0, window_start)
        pipe.zcard(key)
        pipe.zadd(key, {str(current_time): current_time})
        pipe.expire(key, window)
        
        results = await pipe.execute()
        current_requests = results[1]
        
        return current_requests >= limit
```

### Request Routing Service

```python
# src/gateway/services/proxy.py
import aiohttp
import asyncio
from typing import Dict, Optional, Any
import json
import time

class ProxyService:
    def __init__(self):
        self.session_pools: Dict[str, aiohttp.ClientSession] = {}
        self.instance_cache: Dict[str, Dict] = {}
        self.cache_ttl = 300  # 5 minutes
    
    async def initialize_pools(self):
        """Initialize HTTP client session pools"""
        connector = aiohttp.TCPConnector(
            limit=100,
            limit_per_host=20,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        
        self.default_session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={"User-Agent": "AlgoFactory-Gateway/1.0"}
        )
    
    async def cleanup_pools(self):
        """Cleanup HTTP client sessions"""
        if hasattr(self, 'default_session'):
            await self.default_session.close()
        
        for session in self.session_pools.values():
            await session.close()
    
    async def route_request(self, user_id: str, instance_id: str, 
                          endpoint: str, method: str, data: Optional[Dict] = None,
                          params: Optional[Dict] = None) -> Dict:
        """Route request to appropriate OpenAlgo instance"""
        
        # Get instance details
        instance = await self._get_instance_details(user_id, instance_id)
        if not instance:
            raise HTTPException(
                status_code=404,
                detail="Instance not found"
            )
        
        # Check instance status
        if instance['status'] != 'active':
            raise HTTPException(
                status_code=503,
                detail=f"Instance is {instance['status']}"
            )
        
        # Get authenticated session for instance
        session_data = await self._get_authenticated_session(instance)
        
        # Build request URL
        base_url = f"http://{instance['static_ip']}:5000"
        url = f"{base_url}/api/v1/{endpoint}"
        
        # Prepare headers
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "AlgoFactory-Gateway/1.0"
        }
        
        # Add authentication headers/cookies
        if session_data.get('cookies'):
            headers.update(session_data['cookies'])
        
        # Add API key if available
        if instance.get('openalgo_api_key'):
            if data:
                data['apikey'] = instance['openalgo_api_key']
            else:
                params = params or {}
                params['apikey'] = instance['openalgo_api_key']
        
        try:
            # Make request to OpenAlgo instance
            async with self.default_session.request(
                method=method,
                url=url,
                json=data,
                params=params,
                headers=headers
            ) as response:
                
                # Log request metrics
                await self._log_request_metrics(
                    user_id, instance_id, endpoint, method,
                    response.status, response.headers.get('X-Response-Time')
                )
                
                # Handle response
                if response.content_type == 'application/json':
                    response_data = await response.json()
                else:
                    response_data = {"data": await response.text()}
                
                return {
                    "status_code": response.status,
                    "data": response_data,
                    "headers": dict(response.headers)
                }
                
        except aiohttp.ClientError as e:
            raise HTTPException(
                status_code=503,
                detail=f"Instance communication error: {str(e)}"
            )
        except asyncio.TimeoutError:
            raise HTTPException(
                status_code=504,
                detail="Instance request timeout"
            )
    
    async def _get_instance_details(self, user_id: str, instance_id: str) -> Optional[Dict]:
        """Get instance details with caching"""
        cache_key = f"{user_id}:{instance_id}"
        
        # Check cache
        if cache_key in self.instance_cache:
            cached_data = self.instance_cache[cache_key]
            if time.time() - cached_data['timestamp'] < self.cache_ttl:
                return cached_data['data']
        
        # Fetch from database
        # This would be replaced with actual database query
        instance_data = await self._fetch_instance_from_db(user_id, instance_id)
        
        # Cache the result
        if instance_data:
            self.instance_cache[cache_key] = {
                'data': instance_data,
                'timestamp': time.time()
            }
        
        return instance_data
    
    async def _get_authenticated_session(self, instance: Dict) -> Dict:
        """Get or create authenticated session with OpenAlgo instance"""
        
        # Check if we have valid session
        session_key = f"session:{instance['id']}"
        
        # This would implement session management with OpenAlgo
        # For now, return basic session data
        return {
            'cookies': {},
            'csrf_token': None,
            'expires_at': time.time() + 3600
        }
    
    async def _log_request_metrics(self, user_id: str, instance_id: str,
                                 endpoint: str, method: str, status_code: int,
                                 response_time: Optional[str]):
        """Log request metrics for monitoring"""
        
        metrics_data = {
            'user_id': user_id,
            'instance_id': instance_id,
            'endpoint': endpoint,
            'method': method,
            'status_code': status_code,
            'response_time_ms': response_time,
            'timestamp': time.time()
        }
        
        # This would send metrics to monitoring system
        # For now, just log
        print(f"Request metrics: {json.dumps(metrics_data)}")
```

### Trading Router

```python
# src/gateway/routers/trading.py
from fastapi import APIRouter, Depends, Request, HTTPException
from typing import Dict, Any, Optional
from ..services.proxy import ProxyService
from ..models.trading import PlaceOrderRequest, OrderResponse

router = APIRouter()

@router.post("/instances/{instance_id}/orders", response_model=OrderResponse)
async def place_order(
    instance_id: str,
    order_data: PlaceOrderRequest,
    request: Request,
    proxy_service: ProxyService = Depends()
):
    """Place an order through specific instance"""
    
    user_id = request.state.user_id
    
    # Validate user has access to instance
    if not await _validate_instance_access(user_id, instance_id):
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Route request to OpenAlgo instance
    response = await proxy_service.route_request(
        user_id=user_id,
        instance_id=instance_id,
        endpoint="placeorder",
        method="POST",
        data=order_data.dict()
    )
    
    return OrderResponse(**response['data'])

@router.get("/instances/{instance_id}/orders")
async def get_orders(
    instance_id: str,
    request: Request,
    proxy_service: ProxyService = Depends()
):
    """Get order book for specific instance"""
    
    user_id = request.state.user_id
    
    if not await _validate_instance_access(user_id, instance_id):
        raise HTTPException(status_code=403, detail="Access denied")
    
    response = await proxy_service.route_request(
        user_id=user_id,
        instance_id=instance_id,
        endpoint="orderbook",
        method="GET"
    )
    
    return response['data']

@router.get("/instances/{instance_id}/positions")
async def get_positions(
    instance_id: str,
    request: Request,
    proxy_service: ProxyService = Depends()
):
    """Get positions for specific instance"""
    
    user_id = request.state.user_id
    
    if not await _validate_instance_access(user_id, instance_id):
        raise HTTPException(status_code=403, detail="Access denied")
    
    response = await proxy_service.route_request(
        user_id=user_id,
        instance_id=instance_id,
        endpoint="positionbook",
        method="GET"
    )
    
    return response['data']

async def _validate_instance_access(user_id: str, instance_id: str) -> bool:
    """Validate user has access to the instance"""
    # This would check database for user-instance relationship
    return True  # Placeholder
```

## WebSocket Gateway

```python
# src/gateway/websocket.py
from fastapi import WebSocket, WebSocketDisconnect
import asyncio
import json
from typing import Dict, Set

class WebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        self.user_subscriptions: Dict[str, Set[str]] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        
        if user_id not in self.active_connections:
            self.active_connections[user_id] = set()
        
        self.active_connections[user_id].add(websocket)
    
    def disconnect(self, websocket: WebSocket, user_id: str):
        if user_id in self.active_connections:
            self.active_connections[user_id].discard(websocket)
            
            if not self.active_connections[user_id]:
                del self.active_connections[user_id]
    
    async def subscribe_to_instance(self, user_id: str, instance_id: str):
        if user_id not in self.user_subscriptions:
            self.user_subscriptions[user_id] = set()
        
        self.user_subscriptions[user_id].add(instance_id)
        
        # Start forwarding data from instance
        asyncio.create_task(self._forward_instance_data(user_id, instance_id))
    
    async def broadcast_to_user(self, user_id: str, message: Dict):
        if user_id in self.active_connections:
            disconnected = set()
            
            for websocket in self.active_connections[user_id]:
                try:
                    await websocket.send_text(json.dumps(message))
                except:
                    disconnected.add(websocket)
            
            # Remove disconnected websockets
            for websocket in disconnected:
                self.active_connections[user_id].discard(websocket)

manager = WebSocketManager()

@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    await manager.connect(websocket, user_id)
    
    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            
            if message.get('type') == 'subscribe':
                instance_id = message.get('instance_id')
                await manager.subscribe_to_instance(user_id, instance_id)
                
    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id)
```

This API Gateway provides a robust, scalable foundation for routing requests to OpenAlgo instances while maintaining security, performance, and observability.
