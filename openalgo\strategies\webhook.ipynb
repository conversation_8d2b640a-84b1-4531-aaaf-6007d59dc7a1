{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from openalgo import Strategy\n", "\n", "# Initialize strategy client\n", "client = Strategy(\n", "    host_url=\"http://127.0.0.1:5000\",  # Your OpenAlgo server URL\n", "    webhook_id=\"your-strategy-webhook-id\" # Get this from OpenAlgo strategy section\n", ")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'message': 'Order queued successfully for RELIANCE'}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Example 1: Long/Short only mode (configured in OpenAlgo)\n", "#Trading Mode - Long/Short only mode (configured in OpenAlgo)\n", "client.strategyorder(\"RELIANCE\", \"BUY\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'message': 'Order queued successfully for RELIANCE'}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["#Trading Mode - BOTH - Long Entry\n", "client.strategyorder(\"RELIANCE\", \"BUY\",10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 2: BOTH mode (configured in OpenAlgo)\n", "#Trading Mode - BOTH - Long Exit\n", "client.strategyorder(\"RELIANCE\", \"SELL\",10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Trading Mode - BOTH - Short Entry\n", "client.strategyorder(\"RELIANCE\", \"SELL\",10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Trading Mode - BOTH - Short Exit\n", "client.strategyorder(\"RELIANCE\", \"BUY\",0)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 2}