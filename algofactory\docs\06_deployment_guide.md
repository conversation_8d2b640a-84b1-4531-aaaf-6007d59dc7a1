# AlgoFactory - Deployment Guide

## Overview

This guide covers the complete deployment process for AlgoFactory, including infrastructure setup, application deployment, monitoring, and maintenance procedures.

## Infrastructure Requirements

### AWS Services Used
- **AWS Lightsail**: OpenAlgo instance hosting
- **AWS RDS**: PostgreSQL database
- **AWS ElastiCache**: Redis caching
- **AWS ALB**: Load balancing
- **AWS CloudFront**: CDN
- **AWS S3**: Static assets and backups
- **AWS CloudWatch**: Monitoring and logging
- **AWS Route 53**: DNS management

### Estimated Monthly Costs
```
Production Environment:
- RDS PostgreSQL (db.t3.micro): $15/month
- ElastiCache Redis (cache.t3.micro): $12/month
- ALB: $16/month
- CloudFront: $5/month
- S3 Storage: $5/month
- CloudWatch: $10/month
- Route 53: $1/month
- EC2 for Gateway (t3.small): $17/month
Total Infrastructure: ~$81/month

Per Instance Costs:
- Lightsail Basic (512MB): $5/month
- Lightsail Pro (1GB): $10/month
```

## Environment Setup

### 1. Development Environment

```bash
# Clone the repository
git clone https://github.com/yourusername/algofactory.git
cd algofactory

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install Node.js dependencies for frontend
cd frontend
npm install
cd ..

# Setup environment variables
cp .env.example .env
# Edit .env with your configuration

# Setup database
alembic upgrade head

# Run development servers
# Terminal 1: Backend
uvicorn src.gateway.main:app --reload --port 8000

# Terminal 2: Frontend
cd frontend
npm start

# Terminal 3: Worker processes
celery -A src.workers.celery worker --loglevel=info
```

### 2. Environment Variables

```bash
# .env file
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/algofactory
REDIS_URL=redis://localhost:6379/0

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# AWS Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_DEFAULT_REGION=us-east-1

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Application Settings
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Encryption
ENCRYPTION_KEY=your-32-byte-encryption-key
```

## Production Deployment

### 1. Infrastructure as Code (Terraform)

```hcl
# infrastructure/main.tf
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# VPC and Networking
resource "aws_vpc" "algofactory_vpc" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name = "algofactory-vpc"
  }
}

resource "aws_subnet" "public_subnet_1" {
  vpc_id                  = aws_vpc.algofactory_vpc.id
  cidr_block              = "********/24"
  availability_zone       = "${var.aws_region}a"
  map_public_ip_on_launch = true

  tags = {
    Name = "algofactory-public-1"
  }
}

resource "aws_subnet" "public_subnet_2" {
  vpc_id                  = aws_vpc.algofactory_vpc.id
  cidr_block              = "********/24"
  availability_zone       = "${var.aws_region}b"
  map_public_ip_on_launch = true

  tags = {
    Name = "algofactory-public-2"
  }
}

# RDS Database
resource "aws_db_subnet_group" "algofactory_db_subnet_group" {
  name       = "algofactory-db-subnet-group"
  subnet_ids = [aws_subnet.public_subnet_1.id, aws_subnet.public_subnet_2.id]

  tags = {
    Name = "AlgoFactory DB subnet group"
  }
}

resource "aws_db_instance" "algofactory_db" {
  identifier             = "algofactory-db"
  engine                 = "postgres"
  engine_version         = "15.4"
  instance_class         = "db.t3.micro"
  allocated_storage      = 20
  storage_type           = "gp2"
  storage_encrypted      = true

  db_name  = "algofactory"
  username = var.db_username
  password = var.db_password

  vpc_security_group_ids = [aws_security_group.rds_sg.id]
  db_subnet_group_name   = aws_db_subnet_group.algofactory_db_subnet_group.name

  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"

  skip_final_snapshot = false
  final_snapshot_identifier = "algofactory-db-final-snapshot"

  tags = {
    Name = "algofactory-database"
  }
}

# ElastiCache Redis
resource "aws_elasticache_subnet_group" "algofactory_cache_subnet_group" {
  name       = "algofactory-cache-subnet-group"
  subnet_ids = [aws_subnet.public_subnet_1.id, aws_subnet.public_subnet_2.id]
}

resource "aws_elasticache_cluster" "algofactory_redis" {
  cluster_id           = "algofactory-redis"
  engine               = "redis"
  node_type            = "cache.t3.micro"
  num_cache_nodes      = 1
  parameter_group_name = "default.redis7"
  port                 = 6379
  subnet_group_name    = aws_elasticache_subnet_group.algofactory_cache_subnet_group.name
  security_group_ids   = [aws_security_group.redis_sg.id]

  tags = {
    Name = "algofactory-redis"
  }
}

# Application Load Balancer
resource "aws_lb" "algofactory_alb" {
  name               = "algofactory-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb_sg.id]
  subnets            = [aws_subnet.public_subnet_1.id, aws_subnet.public_subnet_2.id]

  enable_deletion_protection = false

  tags = {
    Name = "algofactory-alb"
  }
}

# EC2 Instance for Gateway
resource "aws_instance" "algofactory_gateway" {
  ami           = "ami-0c02fb55956c7d316"  # Amazon Linux 2
  instance_type = "t3.small"
  key_name      = var.key_pair_name

  vpc_security_group_ids = [aws_security_group.gateway_sg.id]
  subnet_id              = aws_subnet.public_subnet_1.id

  user_data = base64encode(templatefile("${path.module}/user_data.sh", {
    db_host     = aws_db_instance.algofactory_db.endpoint
    redis_host  = aws_elasticache_cluster.algofactory_redis.cache_nodes[0].address
  }))

  tags = {
    Name = "algofactory-gateway"
  }
}
```

### 2. Docker Configuration

```dockerfile
# Dockerfile.gateway
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY src/ ./src/
COPY alembic/ ./alembic/
COPY alembic.ini .

# Create non-root user
RUN useradd --create-home --shell /bin/bash app
USER app

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

# Start application
CMD ["uvicorn", "src.gateway.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```dockerfile
# Dockerfile.frontend
FROM node:18-alpine as build

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY src/ ./src/
COPY public/ ./public/

# Build application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built assets
COPY --from=build /app/build /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### 3. Docker Compose for Production

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  gateway:
    build:
      context: .
      dockerfile: Dockerfile.gateway
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.frontend
    ports:
      - "80:80"
    restart: unless-stopped

  worker:
    build:
      context: .
      dockerfile: Dockerfile.gateway
    command: celery -A src.workers.celery worker --loglevel=info
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    depends_on:
      - redis
    restart: unless-stopped

  scheduler:
    build:
      context: .
      dockerfile: Dockerfile.gateway
    command: celery -A src.workers.celery beat --loglevel=info
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    depends_on:
      - redis
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  default:
    name: algofactory-network
```

### 4. Deployment Script

```bash
#!/bin/bash
# deploy.sh

set -e

echo "Starting AlgoFactory deployment..."

# Configuration
ENVIRONMENT=${1:-production}
VERSION=${2:-latest}

# Build and push Docker images
echo "Building Docker images..."
docker build -t algofactory/gateway:$VERSION -f Dockerfile.gateway .
docker build -t algofactory/frontend:$VERSION -f frontend/Dockerfile.frontend ./frontend

# Push to registry (if using Docker registry)
if [ "$ENVIRONMENT" = "production" ]; then
    echo "Pushing images to registry..."
    docker push algofactory/gateway:$VERSION
    docker push algofactory/frontend:$VERSION
fi

# Deploy infrastructure with Terraform
echo "Deploying infrastructure..."
cd infrastructure
terraform init
terraform plan -var-file="$ENVIRONMENT.tfvars"
terraform apply -var-file="$ENVIRONMENT.tfvars" -auto-approve
cd ..

# Run database migrations
echo "Running database migrations..."
docker run --rm \
    -e DATABASE_URL=$DATABASE_URL \
    algofactory/gateway:$VERSION \
    alembic upgrade head

# Deploy application
echo "Deploying application..."
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml up -d

# Health check
echo "Performing health checks..."
sleep 30

# Check gateway health
if curl -f http://localhost:8000/health; then
    echo "Gateway is healthy"
else
    echo "Gateway health check failed"
    exit 1
fi

# Check frontend
if curl -f http://localhost:80; then
    echo "Frontend is healthy"
else
    echo "Frontend health check failed"
    exit 1
fi

echo "Deployment completed successfully!"
```

## Monitoring and Logging

### 1. Prometheus Configuration

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'algofactory-gateway'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'

  - job_name: 'algofactory-instances'
    ec2_sd_configs:
      - region: us-east-1
        port: 8080
        filters:
          - name: tag:Project
            values: [AlgoFactory]
```

### 2. Grafana Dashboard

```json
{
  "dashboard": {
    "title": "AlgoFactory Monitoring",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Active Instances",
        "type": "stat",
        "targets": [
          {
            "expr": "count(up{job=\"algofactory-instances\"} == 1)",
            "legendFormat": "Active Instances"
          }
        ]
      }
    ]
  }
}
```

### 3. Log Aggregation

```yaml
# logging/filebeat.yml
filebeat.inputs:
- type: container
  paths:
    - '/var/lib/docker/containers/*/*.log'
  processors:
    - add_docker_metadata:
        host: "unix:///var/run/docker.sock"

output.elasticsearch:
  hosts: ["elasticsearch:9200"]

setup.kibana:
  host: "kibana:5601"
```

## Backup and Recovery

### 1. Database Backup Script

```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"
DB_NAME="algofactory"

# Create backup
pg_dump $DATABASE_URL > $BACKUP_DIR/algofactory_$DATE.sql

# Upload to S3
aws s3 cp $BACKUP_DIR/algofactory_$DATE.sql s3://algofactory-backups/database/

# Clean up old local backups (keep last 7 days)
find $BACKUP_DIR -name "algofactory_*.sql" -mtime +7 -delete

echo "Backup completed: algofactory_$DATE.sql"
```

### 2. Automated Backup with Cron

```bash
# Add to crontab
0 2 * * * /opt/algofactory/scripts/backup.sh >> /var/log/backup.log 2>&1
```

## Security Hardening

### 1. SSL/TLS Configuration

```nginx
# nginx/ssl.conf
server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /etc/ssl/certs/yourdomain.com.crt;
    ssl_certificate_key /etc/ssl/private/yourdomain.com.key;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 2. Firewall Rules

```bash
# UFW firewall configuration
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw enable
```

This deployment guide provides a comprehensive approach to deploying AlgoFactory in production with proper security, monitoring, and backup procedures.
