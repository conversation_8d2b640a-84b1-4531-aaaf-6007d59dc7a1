"""
Trading Instance model
"""

from sqlalchemy import Column, String, DateTime, ForeignKey, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
import uuid

from app.core.database import Base


class TradingInstance(Base):
    __tablename__ = "trading_instances"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    name = Column(String(100), nullable=False)
    broker = Column(String(50), nullable=False)
    status = Column(String(20), nullable=False)  # creating, active, stopped, error
    lightsail_instance_id = Column(String(255))
    static_ip = Column(String(15))
    internal_url = Column(String(255))
    openalgo_api_key = Column(String(255))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_active = Column(DateTime(timezone=True))

    # Relationships
    user = relationship("User", back_populates="trading_instances")
    broker_credentials = relationship("BrokerCredential", back_populates="instance", cascade="all, delete-orphan")
    metrics = relationship("InstanceMetric", back_populates="instance", cascade="all, delete-orphan")
    api_usage = relationship("ApiUsage", back_populates="instance", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<TradingInstance(id={self.id}, name={self.name}, status={self.status})>"


class BrokerCredential(Base):
    __tablename__ = "broker_credentials"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    instance_id = Column(UUID(as_uuid=True), ForeignKey("trading_instances.id"), nullable=False)
    broker = Column(String(50), nullable=False)
    credentials_encrypted = Column(String, nullable=False)  # JSON encrypted credentials
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    instance = relationship("TradingInstance", back_populates="broker_credentials")

    def __repr__(self):
        return f"<BrokerCredential(id={self.id}, instance_id={self.instance_id}, broker={self.broker})>"


class InstanceMetric(Base):
    __tablename__ = "instance_metrics"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    instance_id = Column(UUID(as_uuid=True), ForeignKey("trading_instances.id"), nullable=False)
    metric_type = Column(String(50))  # cpu, memory, trades, pnl
    value = Column(String(50))  # Store as string for flexibility
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    instance = relationship("TradingInstance", back_populates="metrics")

    def __repr__(self):
        return f"<InstanceMetric(id={self.id}, type={self.metric_type}, value={self.value})>"
