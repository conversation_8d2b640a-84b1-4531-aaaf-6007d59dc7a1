version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: algofactory_postgres
    environment:
      POSTGRES_DB: algofactory
      POSTGRES_USER: algofactory
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - algofactory_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: algofactory_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - algofactory_network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: algofactory_backend
    environment:
      - DATABASE_URL=***********************************************/algofactory
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=development
      - DEBUG=true
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
    depends_on:
      - postgres
      - redis
    networks:
      - algofactory_network
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: algofactory_frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - algofactory_network
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    command: npm start

volumes:
  postgres_data:
  redis_data:

networks:
  algofactory_network:
    driver: bridge
