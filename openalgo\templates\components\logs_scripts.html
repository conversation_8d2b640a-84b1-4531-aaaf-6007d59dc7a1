<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize variables
    var searchTimeout = null;
    var loading = document.getElementById('loading');
    var logsContainer = document.getElementById('logs-container');
    
    // Helper functions
    function showLoading() {
        loading.classList.remove('hidden');
        logsContainer.classList.add('opacity-50');
    }
    
    function hideLoading() {
        loading.classList.add('hidden');
        logsContainer.classList.remove('opacity-50');
    }
    
    function updateURL(params) {
        var url = new URL(window.location);
        Object.keys(params).forEach(function(key) {
            if (params[key]) {
                url.searchParams.set(key, params[key]);
            } else {
                url.searchParams.delete(key);
            }
        });
        window.location.href = url.toString();
    }
    
    function loadPage(page) {
        var params = {
            page: page,
            start_date: document.getElementById('start_date').value,
            end_date: document.getElementById('end_date').value,
            search: document.getElementById('search').value
        };
        updateURL(params);
    }

    window.exportLogs = function() {
        var params = new URLSearchParams({
            start_date: document.getElementById('start_date').value,
            end_date: document.getElementById('end_date').value,
            search: document.getElementById('search').value
        }).toString();

        // Create and click a temporary link
        var downloadUrl = `/logs/export?${params}`;
        window.location.href = downloadUrl;
    };
    
    // Set up event listeners for collapse elements
    document.querySelectorAll('[id^="details-collapse-"]').forEach(function(collapse) {
        collapse.addEventListener('change', function() {
            // The checkbox state controls the collapse state in daisyUI
            // No additional handling needed as it's handled by daisyUI classes
        });
    });
    
    // Pagination
    document.querySelectorAll('.page-button').forEach(function(button) {
        button.addEventListener('click', function() {
            loadPage(this.dataset.page);
        });
    });
    
    // Date filters
    document.getElementById('start_date').addEventListener('change', function() {
        loadPage(1);
    });
    
    document.getElementById('end_date').addEventListener('change', function() {
        loadPage(1);
    });
    
    // Search with debounce
    document.getElementById('search').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            loadPage(1);
        }, 500);
    });
    
    // Initialize date inputs if empty
    if (!document.getElementById('start_date').value) {
        var today = new Date().toISOString().split('T')[0];
        document.getElementById('start_date').value = today;
        document.getElementById('end_date').value = today;
    }

    // Initialize tooltips
    document.querySelectorAll('.tooltip').forEach(function(tooltip) {
        // DaisyUI handles tooltips automatically through classes
    });
});
</script>
