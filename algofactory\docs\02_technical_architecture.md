# AlgoFactory - Technical Architecture

## System Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │   Mobile App    │    │   API Clients   │
│   (React.js)    │    │   (React Native)│    │   (External)    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │     Load Balancer         │
                    │     (AWS ALB)             │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │   AlgoFactory Gateway     │
                    │   (FastAPI/Flask)         │
                    └─────────────┬─────────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                       │                        │
┌───────┴────────┐    ┌─────────┴─────────┐    ┌─────────┴─────────┐
│   User Service │    │ Instance Service  │    │  Billing Service  │
│   (Auth/Users) │    │ (OpenAlgo Mgmt)   │    │  (Payments)       │
└────────────────┘    └───────────────────┘    └───────────────────┘
        │                       │                        │
        │              ┌────────┴────────┐               │
        │              │                 │               │
        │         ┌────┴─────┐    ┌─────┴─────┐         │
        │         │ Instance │    │ Instance  │         │
        │         │    #1    │    │    #2     │         │
        │         │(OpenAlgo)│    │(OpenAlgo) │         │
        │         └──────────┘    └───────────┘         │
        │                                               │
        └─────────────────┬─────────────────────────────┘
                         │
                ┌────────┴────────┐
                │   PostgreSQL    │
                │   (Main DB)     │
                └─────────────────┘
```

## Core Components

### 1. AlgoFactory Gateway
**Technology**: FastAPI (Python)
**Purpose**: Central API gateway and orchestration layer

**Responsibilities**:
- User authentication and authorization
- Request routing to appropriate services
- Rate limiting and throttling
- API key management
- Request/response transformation

**Key Features**:
- JWT-based authentication
- Role-based access control (RBAC)
- API versioning
- Request logging and monitoring
- Circuit breaker pattern

### 2. User Service
**Technology**: FastAPI + SQLAlchemy
**Purpose**: User management and authentication

**Responsibilities**:
- User registration and login
- Profile management
- Subscription handling
- Multi-factor authentication
- Password reset functionality

**Database Tables**:
```sql
users (id, email, password_hash, created_at, updated_at, is_active)
user_profiles (user_id, first_name, last_name, phone, company)
subscriptions (user_id, plan_type, status, expires_at)
user_sessions (user_id, session_token, expires_at)
```

### 3. Instance Service
**Technology**: FastAPI + AWS SDK
**Purpose**: OpenAlgo instance lifecycle management

**Responsibilities**:
- Instance creation and deletion
- AWS Lightsail integration
- Instance health monitoring
- Broker credential management
- Instance scaling

**Key Components**:
- Instance provisioner
- Health checker
- Credential vault
- Monitoring agent

### 4. Billing Service
**Technology**: FastAPI + Stripe API
**Purpose**: Payment processing and billing

**Responsibilities**:
- Subscription management
- Payment processing
- Invoice generation
- Usage tracking
- Billing analytics

## Data Architecture

### Primary Database (PostgreSQL)
```sql
-- Users and Authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- User Subscriptions
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    plan_type VARCHAR(50) NOT NULL, -- basic, pro, enterprise
    status VARCHAR(20) NOT NULL, -- active, cancelled, expired
    stripe_subscription_id VARCHAR(255),
    current_period_start TIMESTAMP,
    current_period_end TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Trading Instances
CREATE TABLE trading_instances (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    name VARCHAR(100) NOT NULL,
    broker VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL, -- creating, active, stopped, error
    lightsail_instance_id VARCHAR(255),
    static_ip VARCHAR(15),
    internal_url VARCHAR(255),
    openalgo_api_key VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    last_active TIMESTAMP
);

-- Broker Credentials (Encrypted)
CREATE TABLE broker_credentials (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    instance_id UUID REFERENCES trading_instances(id),
    broker VARCHAR(50) NOT NULL,
    credentials_encrypted TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Instance Metrics
CREATE TABLE instance_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    instance_id UUID REFERENCES trading_instances(id),
    metric_type VARCHAR(50), -- cpu, memory, trades, pnl
    value DECIMAL(15,2),
    timestamp TIMESTAMP DEFAULT NOW()
);

-- API Usage Tracking
CREATE TABLE api_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    instance_id UUID REFERENCES trading_instances(id),
    endpoint VARCHAR(255),
    method VARCHAR(10),
    status_code INTEGER,
    response_time_ms INTEGER,
    timestamp TIMESTAMP DEFAULT NOW()
);
```

### Cache Layer (Redis)
```
# Session Management
session:{session_id} -> {user_id, expires_at, permissions}

# Instance Status Cache
instance:{instance_id}:status -> {status, last_check, health}

# Rate Limiting
rate_limit:{user_id}:{endpoint} -> {count, window_start}

# Real-time Data Cache
realtime:{instance_id}:trades -> [trade_data]
realtime:{instance_id}:positions -> [position_data]
```

## AWS Infrastructure

### Lightsail Configuration
```yaml
Instance Specifications:
  - Type: $5/month (1 vCPU, 512 MB RAM) for basic instances
  - Type: $10/month (1 vCPU, 1 GB RAM) for pro instances
  - OS: Ubuntu 20.04 LTS
  - Static IP: Enabled for all instances
  - Firewall: Custom rules for OpenAlgo ports

Networking:
  - VPC: Custom VPC for isolation
  - Security Groups: Restricted access
  - Load Balancer: Application Load Balancer
```

### Instance Provisioning Script
```bash
#!/bin/bash
# OpenAlgo Instance Setup Script

# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Create OpenAlgo directory
mkdir -p /opt/openalgo
cd /opt/openalgo

# Download OpenAlgo
git clone https://github.com/marketcalls/openalgo.git .

# Configure environment
cat > .env << EOF
DATABASE_URL=sqlite:///db/openalgo.db
APP_KEY=${INSTANCE_SECRET_KEY}
BROKER_API_KEY=${BROKER_API_KEY}
BROKER_API_SECRET=${BROKER_API_SECRET}
HOST_SERVER=http://${STATIC_IP}:5000
EOF

# Start OpenAlgo
docker-compose up -d

# Setup auto-user creation
python3 setup_auto_user.py
```

## Security Architecture

### Authentication Flow
1. User logs in with email/password
2. Server validates credentials
3. JWT token issued with expiration
4. Token includes user permissions and instance access
5. All API requests validated against JWT

### Data Encryption
- **At Rest**: AES-256 encryption for sensitive data
- **In Transit**: TLS 1.3 for all communications
- **Broker Credentials**: Encrypted with user-specific keys
- **Database**: Encrypted columns for sensitive data

### Access Control
```python
# Role-Based Access Control
ROLES = {
    'user': ['read_own_instances', 'create_instance', 'manage_own_data'],
    'admin': ['read_all_instances', 'manage_all_users', 'system_config'],
    'support': ['read_user_data', 'assist_users']
}

# Instance-Level Permissions
INSTANCE_PERMISSIONS = {
    'owner': ['read', 'write', 'delete', 'configure'],
    'viewer': ['read'],
    'trader': ['read', 'trade']
}
```

## API Design

### RESTful Endpoints
```
# Authentication
POST /api/v1/auth/register
POST /api/v1/auth/login
POST /api/v1/auth/logout
POST /api/v1/auth/refresh

# User Management
GET /api/v1/users/profile
PUT /api/v1/users/profile
GET /api/v1/users/subscription

# Instance Management
GET /api/v1/instances
POST /api/v1/instances
GET /api/v1/instances/{id}
PUT /api/v1/instances/{id}
DELETE /api/v1/instances/{id}
POST /api/v1/instances/{id}/start
POST /api/v1/instances/{id}/stop

# Trading Operations (Proxied to OpenAlgo)
POST /api/v1/instances/{id}/orders
GET /api/v1/instances/{id}/orders
GET /api/v1/instances/{id}/positions
GET /api/v1/instances/{id}/trades
```

### WebSocket Endpoints
```
# Real-time Updates
/ws/instances/{id}/trades
/ws/instances/{id}/positions
/ws/instances/{id}/status
/ws/notifications
```

## Monitoring and Observability

### Metrics Collection
- **Application Metrics**: Request rates, response times, error rates
- **Business Metrics**: User signups, instance creation, revenue
- **Infrastructure Metrics**: CPU, memory, disk usage
- **Trading Metrics**: Trade volume, P&L, success rates

### Logging Strategy
```python
# Structured Logging Format
{
    "timestamp": "2024-01-01T12:00:00Z",
    "level": "INFO",
    "service": "instance-service",
    "user_id": "uuid",
    "instance_id": "uuid",
    "action": "create_instance",
    "broker": "zerodha",
    "duration_ms": 1500,
    "status": "success"
}
```

### Alerting Rules
- Instance creation failures
- High error rates (>5%)
- Response time degradation (>500ms)
- Payment failures
- Security incidents

This architecture provides a scalable, secure, and maintainable foundation for AlgoFactory while keeping OpenAlgo instances completely isolated and automated.
