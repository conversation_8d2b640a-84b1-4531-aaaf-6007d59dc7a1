import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface TradingInstance {
  id: string;
  name: string;
  broker: string;
  status: 'creating' | 'active' | 'stopped' | 'error';
  staticIp?: string;
  createdAt: string;
  lastActive?: string;
}

interface InstancesState {
  instances: TradingInstance[];
  loading: boolean;
  error: string | null;
}

const initialState: InstancesState = {
  instances: [],
  loading: false,
  error: null,
};

const instancesSlice = createSlice({
  name: 'instances',
  initialState,
  reducers: {
    fetchInstancesStart: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchInstancesSuccess: (state, action: PayloadAction<TradingInstance[]>) => {
      state.loading = false;
      state.instances = action.payload;
    },
    fetchInstancesFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },
    addInstance: (state, action: PayloadAction<TradingInstance>) => {
      state.instances.push(action.payload);
    },
    updateInstance: (state, action: PayloadAction<TradingInstance>) => {
      const index = state.instances.findIndex(instance => instance.id === action.payload.id);
      if (index !== -1) {
        state.instances[index] = action.payload;
      }
    },
    removeInstance: (state, action: PayloadAction<string>) => {
      state.instances = state.instances.filter(instance => instance.id !== action.payload);
    },
  },
});

export const {
  fetchInstancesStart,
  fetchInstancesSuccess,
  fetchInstancesFailure,
  addInstance,
  updateInstance,
  removeInstance,
} = instancesSlice.actions;

export default instancesSlice.reducer;
