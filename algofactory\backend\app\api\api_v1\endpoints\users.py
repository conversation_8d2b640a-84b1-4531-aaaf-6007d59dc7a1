"""
User management endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.deps import get_current_active_user
from app.schemas.user import User, UserUpdate
from app.services.user_service import UserService
from app.models.user import User as UserModel

router = APIRouter()


@router.get("/profile", response_model=User)
async def get_profile(current_user: UserModel = Depends(get_current_active_user)):
    """Get user profile"""
    return current_user


@router.put("/profile", response_model=User)
async def update_profile(
    user_data: UserUpdate,
    current_user: UserModel = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update user profile"""
    user_service = UserService(db)
    updated_user = user_service.update_user(str(current_user.id), user_data)

    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return updated_user


@router.get("/subscription")
async def get_subscription(current_user: UserModel = Depends(get_current_active_user)):
    """Get user subscription details"""
    # TODO: Implement subscription logic
    return {
        "user_id": str(current_user.id),
        "plan_type": "free_trial",
        "status": "active",
        "message": "Subscription endpoint - to be implemented"
    }
