-- AlgoFactory Database Initialization Script

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HA<PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- User subscriptions
CREATE TABLE IF NOT EXISTS subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    plan_type VARCHAR(50) NOT NULL, -- basic, pro, enterprise
    status VARCHAR(20) NOT NULL, -- active, cancelled, expired
    stripe_subscription_id VARCHAR(255),
    current_period_start TIMESTAMP,
    current_period_end TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Trading instances
CREATE TABLE IF NOT EXISTS trading_instances (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    broker VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL, -- creating, active, stopped, error
    lightsail_instance_id VARCHAR(255),
    static_ip VARCHAR(15),
    internal_url VARCHAR(255),
    openalgo_api_key VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    last_active TIMESTAMP
);

-- Broker credentials (encrypted)
CREATE TABLE IF NOT EXISTS broker_credentials (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    instance_id UUID REFERENCES trading_instances(id) ON DELETE CASCADE,
    broker VARCHAR(50) NOT NULL,
    credentials_encrypted TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Instance metrics
CREATE TABLE IF NOT EXISTS instance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    instance_id UUID REFERENCES trading_instances(id) ON DELETE CASCADE,
    metric_type VARCHAR(50), -- cpu, memory, trades, pnl
    value DECIMAL(15,2),
    timestamp TIMESTAMP DEFAULT NOW()
);

-- API usage tracking
CREATE TABLE IF NOT EXISTS api_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    instance_id UUID REFERENCES trading_instances(id) ON DELETE CASCADE,
    endpoint VARCHAR(255),
    method VARCHAR(10),
    status_code INTEGER,
    response_time_ms INTEGER,
    timestamp TIMESTAMP DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_trading_instances_user_id ON trading_instances(user_id);
CREATE INDEX IF NOT EXISTS idx_trading_instances_status ON trading_instances(status);
CREATE INDEX IF NOT EXISTS idx_broker_credentials_instance_id ON broker_credentials(instance_id);
CREATE INDEX IF NOT EXISTS idx_instance_metrics_instance_id ON instance_metrics(instance_id);
CREATE INDEX IF NOT EXISTS idx_instance_metrics_timestamp ON instance_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_api_usage_user_id ON api_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_api_usage_timestamp ON api_usage(timestamp);
