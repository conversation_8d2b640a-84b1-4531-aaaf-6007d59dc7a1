@echo off
REM AlgoFactory Development Startup Script for Windows

echo 🚀 Starting AlgoFactory Development Environment...

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker first.
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist .env (
    echo 📝 Creating .env file from template...
    copy .env.example .env
    echo ✅ Please edit .env file with your configuration before continuing.
    echo    Then run this script again.
    pause
    exit /b 1
)

REM Start services with Docker Compose
echo 🐳 Starting Docker containers...
docker-compose up -d

REM Wait for services to be ready
echo ⏳ Waiting for services to start...
timeout /t 10 /nobreak >nul

REM Check if services are running
docker-compose ps | findstr "Up" >nul
if errorlevel 1 (
    echo ❌ Some services failed to start. Check logs with:
    echo    docker-compose logs
) else (
    echo ✅ Services started successfully!
    echo.
    echo 🌐 Application URLs:
    echo    Frontend: http://localhost:3000
    echo    Backend API: http://localhost:8000
    echo    API Docs: http://localhost:8000/docs
    echo.
    echo 📊 Database:
    echo    PostgreSQL: localhost:5432
    echo    Redis: localhost:6379
    echo.
    echo 🔧 Development Commands:
    echo    View logs: docker-compose logs -f
    echo    Stop services: docker-compose down
    echo    Restart: docker-compose restart
)

pause
